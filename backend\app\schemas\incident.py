from pydantic import BaseModel, validator
from typing import Optional, List
from datetime import datetime
from ..models.incident import IncidentType, SeverityLevel, IncidentStatus

class IncidentBase(BaseModel):
    incident_title: str
    incident_type: IncidentType
    incident_date: datetime
    affected_system: str
    impact_description: str
    severity_level: SeverityLevel
    actions_taken: Optional[str] = None
    status: IncidentStatus = IncidentStatus.OPEN
    assigned_to: Optional[int] = None

class IncidentCreate(IncidentBase):
    @validator('incident_title')
    def validate_title(cls, v):
        if len(v.strip()) < 5:
            raise ValueError('Incident title must be at least 5 characters long')
        if len(v) > 200:
            raise ValueError('Incident title must be less than 200 characters')
        return v.strip()
    
    @validator('affected_system')
    def validate_affected_system(cls, v):
        if len(v.strip()) < 2:
            raise ValueError('Affected system must be at least 2 characters long')
        if len(v) > 200:
            raise ValueError('Affected system must be less than 200 characters')
        return v.strip()
    
    @validator('impact_description')
    def validate_impact_description(cls, v):
        if len(v.strip()) < 10:
            raise ValueError('Impact description must be at least 10 characters long')
        return v.strip()

class IncidentUpdate(BaseModel):
    incident_title: Optional[str] = None
    incident_type: Optional[IncidentType] = None
    incident_date: Optional[datetime] = None
    affected_system: Optional[str] = None
    impact_description: Optional[str] = None
    severity_level: Optional[SeverityLevel] = None
    actions_taken: Optional[str] = None
    status: Optional[IncidentStatus] = None
    assigned_to: Optional[int] = None
    
    @validator('incident_title')
    def validate_title(cls, v):
        if v is not None:
            if len(v.strip()) < 5:
                raise ValueError('Incident title must be at least 5 characters long')
            if len(v) > 200:
                raise ValueError('Incident title must be less than 200 characters')
            return v.strip()
        return v
    
    @validator('affected_system')
    def validate_affected_system(cls, v):
        if v is not None:
            if len(v.strip()) < 2:
                raise ValueError('Affected system must be at least 2 characters long')
            if len(v) > 200:
                raise ValueError('Affected system must be less than 200 characters')
            return v.strip()
        return v
    
    @validator('impact_description')
    def validate_impact_description(cls, v):
        if v is not None:
            if len(v.strip()) < 10:
                raise ValueError('Impact description must be at least 10 characters long')
            return v.strip()
        return v

class IncidentResponse(IncidentBase):
    id: int
    reporter_id: int
    attachments: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    # Related objects
    reporter: Optional[dict] = None
    assignee: Optional[dict] = None
    
    class Config:
        from_attributes = True

class IncidentFilter(BaseModel):
    incident_type: Optional[IncidentType] = None
    severity_level: Optional[SeverityLevel] = None
    status: Optional[IncidentStatus] = None
    reporter_id: Optional[int] = None
    assigned_to: Optional[int] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    search: Optional[str] = None

class IncidentStats(BaseModel):
    total_incidents: int
    open_incidents: int
    closed_incidents: int
    critical_incidents: int
    high_priority_incidents: int
    incidents_by_type: dict
    incidents_by_status: dict
    incidents_by_severity: dict
