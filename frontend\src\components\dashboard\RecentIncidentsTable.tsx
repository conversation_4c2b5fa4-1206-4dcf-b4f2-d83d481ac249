import React from 'react';
import { Incident } from '@/types';
import { 
  ExclamationCircleIcon,
  CalendarIcon,
  UserIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface RecentIncidentsTableProps {
  incidents: Incident[];
}

const RecentIncidentsTable: React.FC<RecentIncidentsTableProps> = ({ incidents }) => {
  const formatSeverity = (severity: string) => {
    const severityMap: Record<string, string> = {
      'CRITICAL': 'حرج',
      'HIGH': 'عالي',
      'MEDIUM': 'متوسط',
      'LOW': 'منخفض'
    };
    return severityMap[severity] || severity;
  };

  const formatStatus = (status: string) => {
    const statusMap: Record<string, string> = {
      'OPEN': 'مفتوح',
      'IN_PROGRESS': 'قيد المعالجة',
      'UNDER_INVESTIGATION': 'قيد التحقيق',
      'CLOSED': 'مغلق'
    };
    return statusMap[status] || status;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  if (incidents.length === 0) {
    return (
      <div className="text-center py-12">
        <ExclamationCircleIcon className="h-12 w-12 mx-auto mb-4 text-gray-500" />
        <p className="text-lg font-medium text-gray-400">لا توجد حوادث حديثة</p>
        <p className="text-sm text-gray-500">سيتم عرض الحوادث الجديدة هنا عند إضافتها</p>
      </div>
    );
  }

  return (
    <div className="overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-dark-600">
          <thead className="bg-dark-700">
            <tr>
              <th className="px-6 py-4 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                عنوان الحادث
              </th>
              <th className="px-6 py-4 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                مستوى الخطورة
              </th>
              <th className="px-6 py-4 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                الحالة
              </th>
              <th className="hidden md:table-cell px-6 py-4 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                تاريخ الإدخال
              </th>
              <th className="hidden lg:table-cell px-6 py-4 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                المسؤول
              </th>
            </tr>
          </thead>
          <tbody className="bg-dark-800 divide-y divide-dark-600">
            {incidents.map((incident) => (
              <tr 
                key={incident.id} 
                className="hover:bg-dark-700 transition-colors duration-150 cursor-pointer"
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <div className="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center">
                        <ExclamationCircleIcon className="h-5 w-5 text-white" />
                      </div>
                    </div>
                    <div className="mr-4">
                      <div className="text-sm font-medium text-gray-100">
                        {incident.incident_title}
                      </div>
                      <div className="text-sm text-gray-400">
                        {incident.affected_system}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium badge badge-${incident.severity_level.toLowerCase()}`}>
                    {formatSeverity(incident.severity_level)}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium badge badge-${incident.status.toLowerCase().replace('_', '-')}`}>
                    {formatStatus(incident.status)}
                  </span>
                </td>
                <td className="hidden md:table-cell px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                  <div className="flex items-center">
                    <CalendarIcon className="h-4 w-4 text-gray-400 ml-2" />
                    <div>
                      <div>{formatDate(incident.incident_date)}</div>
                      <div className="text-xs text-gray-500 flex items-center">
                        <ClockIcon className="h-3 w-3 ml-1" />
                        {formatTime(incident.incident_date)}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="hidden lg:table-cell px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                  <div className="flex items-center">
                    <UserIcon className="h-4 w-4 text-gray-400 ml-2" />
                    <div>
                      <div>{incident.reporter?.username || 'غير محدد'}</div>
                      <div className="text-xs text-gray-500">
                        {incident.assigned_to?.username || 'غير مُعيَّن'}
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default RecentIncidentsTable;
