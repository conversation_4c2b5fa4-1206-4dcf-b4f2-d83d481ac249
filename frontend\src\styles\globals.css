@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
}

[dir="ltr"] {
  direction: ltr;
}

/* Base styles */
html {
  scroll-behavior: smooth;
}

body {
  @apply bg-dark-950 text-gray-100 font-arabic;
  font-feature-settings: 'liga' 1, 'calt' 1;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-dark-900;
}

::-webkit-scrollbar-thumb {
  @apply bg-dark-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-dark-500;
}

/* Custom components */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-dark-900;
  }
  
  .btn-secondary {
    @apply bg-dark-700 hover:bg-dark-600 text-gray-200 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-dark-500 focus:ring-offset-2 focus:ring-offset-dark-900;
  }
  
  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-dark-900;
  }
  
  .input-field {
    @apply bg-dark-800 border border-dark-600 text-gray-100 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200;
  }
  
  .card {
    @apply bg-dark-800 border border-dark-700 rounded-lg shadow-lg;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-dark-700;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-critical {
    @apply bg-red-600 text-red-100 shadow-lg border border-red-500;
  }

  .badge-high {
    @apply bg-orange-600 text-orange-100 shadow-lg border border-orange-500;
  }

  .badge-medium {
    @apply bg-yellow-600 text-yellow-100 shadow-lg border border-yellow-500;
  }

  .badge-low {
    @apply bg-green-600 text-green-100 shadow-lg border border-green-500;
  }

  .badge-open {
    @apply bg-red-600 text-red-100 shadow-lg border border-red-500;
  }

  .badge-in-progress {
    @apply bg-yellow-600 text-yellow-100 shadow-lg border border-yellow-500;
  }

  .badge-under-investigation {
    @apply bg-blue-600 text-blue-100 shadow-lg border border-blue-500;
  }

  .badge-closed {
    @apply bg-green-600 text-green-100 shadow-lg border border-green-500;
  }
  
  .table-header {
    @apply bg-dark-700 text-gray-300 text-xs font-medium uppercase tracking-wider;
  }
  
  .table-row {
    @apply bg-dark-800 hover:bg-dark-700 transition-colors duration-150;
  }
  
  .sidebar-link {
    @apply flex items-center px-4 py-3 text-sm font-medium text-gray-300 rounded-xl hover:bg-dark-700 hover:text-white transition-all duration-300 relative;
  }

  .sidebar-link.active {
    @apply bg-primary-600 text-white shadow-lg border-r-4 border-primary-400;
  }

  .sidebar-link.active::before {
    content: '';
    @apply absolute right-0 top-0 bottom-0 w-1 bg-primary-400 rounded-l-full;
  }

  .sidebar-link:hover:not(.active) {
    @apply bg-dark-700 text-white transform translate-x-1;
  }
}

/* Animation utilities */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s infinite;
  }
}

/* Loading spinner */
.spinner {
  @apply inline-block w-4 h-4 border-2 border-current border-r-transparent rounded-full animate-spin;
}

/* Form validation styles */
.form-error {
  @apply text-red-400 text-sm mt-1;
}

.form-success {
  @apply text-green-400 text-sm mt-1;
}

/* Custom focus styles for accessibility */
.focus-visible:focus-visible {
  @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-dark-900;
}

/* Print styles */
@media print {
  body {
    @apply bg-white text-black;
  }
  
  .no-print {
    display: none !important;
  }
}
