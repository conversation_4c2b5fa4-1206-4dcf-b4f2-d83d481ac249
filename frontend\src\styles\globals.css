@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
}

[dir="ltr"] {
  direction: ltr;
}

/* Base styles */
html {
  scroll-behavior: smooth;
}

body {
  @apply bg-dark-950 text-gray-100 font-arabic;
  font-feature-settings: 'liga' 1, 'calt' 1;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-dark-900;
}

::-webkit-scrollbar-thumb {
  @apply bg-dark-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-dark-500;
}

/* Custom components */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-dark-900;
  }
  
  .btn-secondary {
    @apply bg-dark-700 hover:bg-dark-600 text-gray-200 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-dark-500 focus:ring-offset-2 focus:ring-offset-dark-900;
  }
  
  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-dark-900;
  }
  
  .input-field {
    @apply bg-dark-800 border border-dark-600 text-gray-100 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200;
  }
  
  .card {
    @apply bg-dark-800 border border-dark-700 rounded-lg shadow-lg;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-dark-700;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-critical {
    @apply bg-red-900 text-red-200;
  }
  
  .badge-high {
    @apply bg-orange-900 text-orange-200;
  }
  
  .badge-medium {
    @apply bg-yellow-900 text-yellow-200;
  }
  
  .badge-low {
    @apply bg-green-900 text-green-200;
  }
  
  .badge-open {
    @apply bg-red-900 text-red-200;
  }
  
  .badge-in-progress {
    @apply bg-yellow-900 text-yellow-200;
  }
  
  .badge-under-investigation {
    @apply bg-blue-900 text-blue-200;
  }
  
  .badge-closed {
    @apply bg-green-900 text-green-200;
  }
  
  .table-header {
    @apply bg-dark-700 text-gray-300 text-xs font-medium uppercase tracking-wider;
  }
  
  .table-row {
    @apply bg-dark-800 hover:bg-dark-700 transition-colors duration-150;
  }
  
  .sidebar-link {
    @apply flex items-center px-4 py-2 text-gray-300 hover:bg-dark-700 hover:text-white transition-colors duration-200 rounded-lg;
  }
  
  .sidebar-link.active {
    @apply bg-primary-600 text-white;
  }
}

/* Animation utilities */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s infinite;
  }
}

/* Loading spinner */
.spinner {
  @apply inline-block w-4 h-4 border-2 border-current border-r-transparent rounded-full animate-spin;
}

/* Form validation styles */
.form-error {
  @apply text-red-400 text-sm mt-1;
}

.form-success {
  @apply text-green-400 text-sm mt-1;
}

/* Custom focus styles for accessibility */
.focus-visible:focus-visible {
  @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-dark-900;
}

/* Print styles */
@media print {
  body {
    @apply bg-white text-black;
  }
  
  .no-print {
    display: none !important;
  }
}
