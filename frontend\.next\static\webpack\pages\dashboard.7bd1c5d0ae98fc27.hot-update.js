"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "./src/components/dashboard/RecentIncidentsTable.tsx":
/*!***********************************************************!*\
  !*** ./src/components/dashboard/RecentIncidentsTable.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_ExclamationCircleIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,ExclamationCircleIcon,UserIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=CalendarIcon,ClockIcon,ExclamationCircleIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\n\n\nconst RecentIncidentsTable = (param)=>{\n    let { incidents } = param;\n    const formatSeverity = (severity)=>{\n        const severityMap = {\n            \"CRITICAL\": \"حرج\",\n            \"HIGH\": \"عالي\",\n            \"MEDIUM\": \"متوسط\",\n            \"LOW\": \"منخفض\"\n        };\n        return severityMap[severity] || severity;\n    };\n    const formatStatus = (status)=>{\n        const statusMap = {\n            \"OPEN\": \"مفتوح\",\n            \"IN_PROGRESS\": \"قيد المعالجة\",\n            \"UNDER_INVESTIGATION\": \"قيد التحقيق\",\n            \"CLOSED\": \"مغلق\"\n        };\n        return statusMap[status] || status;\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString(\"ar-SA\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\"\n        });\n    };\n    const formatTime = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleTimeString(\"ar-SA\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: false\n        });\n    };\n    if (incidents.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_ExclamationCircleIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.ExclamationCircleIcon, {\n                    className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg font-medium text-gray-400\",\n                    children: \"لا توجد حوادث حديثة\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"سيتم عرض الحوادث الجديدة هنا عند إضافتها\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"min-w-full divide-y divide-dark-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        className: \"bg-dark-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-6 py-4 text-right text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                    children: \"عنوان الحادث\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-6 py-4 text-right text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                    children: \"مستوى الخطورة\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-6 py-4 text-right text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                    children: \"الحالة\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"hidden md:table-cell px-6 py-4 text-right text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                    children: \"تاريخ الإدخال\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"hidden lg:table-cell px-6 py-4 text-right text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                    children: \"المسؤول\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        className: \"bg-dark-800 divide-y divide-dark-600\",\n                        children: incidents.map((incident)=>{\n                            var _incident_reporter, _incident_assigned_to;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-dark-700 transition-colors duration-150 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 h-10 w-10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_ExclamationCircleIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.ExclamationCircleIcon, {\n                                                            className: \"h-5 w-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                                            lineNumber: 96,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mr-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-100\",\n                                                            children: incident.incident_title\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: incident.affected_system\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium badge badge-\".concat(incident.severity_level.toLowerCase()),\n                                            children: formatSeverity(incident.severity_level)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium badge badge-\".concat(incident.status.toLowerCase().replace(\"_\", \"-\")),\n                                            children: formatStatus(incident.status)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_ExclamationCircleIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.CalendarIcon, {\n                                                    className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: formatDate(incident.incident_date)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_ExclamationCircleIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.ClockIcon, {\n                                                                    className: \"h-3 w-3 ml-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                                                    lineNumber: 125,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                formatTime(incident.incident_date)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_ExclamationCircleIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.UserIcon, {\n                                                    className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: ((_incident_reporter = incident.reporter) === null || _incident_reporter === void 0 ? void 0 : _incident_reporter.username) || \"غير محدد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: ((_incident_assigned_to = incident.assigned_to) === null || _incident_assigned_to === void 0 ? void 0 : _incident_assigned_to.username) || \"غير مُعيَّن\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, incident.id, true, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentIncidentsTable.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n_c = RecentIncidentsTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (RecentIncidentsTable);\nvar _c;\n$RefreshReg$(_c, \"RecentIncidentsTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/dashboard/RecentIncidentsTable.tsx\n"));

/***/ })

});