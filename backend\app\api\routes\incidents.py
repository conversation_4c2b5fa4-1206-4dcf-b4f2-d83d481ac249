from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import List, Optional
import json
import os
import uuid
from datetime import datetime

from ...core.database import get_db
from ...core.security import get_current_user
from ...core.config import settings
from ...models.user import User
from ...models.incident import Incident, IncidentType, SeverityLevel, IncidentStatus
from ...schemas.incident import (
    IncidentCreate, IncidentUpdate, IncidentResponse, 
    IncidentFilter, IncidentStats
)

router = APIRouter()

@router.get("/", response_model=List[IncidentResponse])
async def get_incidents(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    incident_type: Optional[IncidentType] = None,
    severity_level: Optional[SeverityLevel] = None,
    status: Optional[IncidentStatus] = None,
    reporter_id: Optional[int] = None,
    assigned_to: Optional[int] = None,
    date_from: Optional[datetime] = None,
    date_to: Optional[datetime] = None,
    search: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get list of incidents with filtering."""
    query = db.query(Incident)
    
    # Apply filters
    if incident_type:
        query = query.filter(Incident.incident_type == incident_type)
    if severity_level:
        query = query.filter(Incident.severity_level == severity_level)
    if status:
        query = query.filter(Incident.status == status)
    if reporter_id:
        query = query.filter(Incident.reporter_id == reporter_id)
    if assigned_to:
        query = query.filter(Incident.assigned_to == assigned_to)
    if date_from:
        query = query.filter(Incident.incident_date >= date_from)
    if date_to:
        query = query.filter(Incident.incident_date <= date_to)
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                Incident.incident_title.ilike(search_term),
                Incident.affected_system.ilike(search_term),
                Incident.impact_description.ilike(search_term)
            )
        )
    
    # Order by creation date (newest first)
    query = query.order_by(Incident.created_at.desc())
    
    # Apply pagination
    incidents = query.offset(skip).limit(limit).all()
    
    # Add related user information
    result = []
    for incident in incidents:
        incident_dict = {
            "id": incident.id,
            "incident_title": incident.incident_title,
            "incident_type": incident.incident_type,
            "incident_date": incident.incident_date,
            "affected_system": incident.affected_system,
            "impact_description": incident.impact_description,
            "severity_level": incident.severity_level,
            "actions_taken": incident.actions_taken,
            "status": incident.status,
            "attachments": incident.attachments,
            "reporter_id": incident.reporter_id,
            "assigned_to": incident.assigned_to,
            "created_at": incident.created_at,
            "updated_at": incident.updated_at,
            "reporter": {
                "id": incident.reporter.id,
                "username": incident.reporter.username
            } if incident.reporter else None,
            "assignee": {
                "id": incident.assignee.id,
                "username": incident.assignee.username
            } if incident.assignee else None
        }
        result.append(incident_dict)
    
    return result

@router.get("/{incident_id}", response_model=IncidentResponse)
async def get_incident(
    incident_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get incident by ID."""
    incident = db.query(Incident).filter(Incident.id == incident_id).first()
    if not incident:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Incident not found"
        )
    
    # Build response with related user information
    incident_dict = {
        "id": incident.id,
        "incident_title": incident.incident_title,
        "incident_type": incident.incident_type,
        "incident_date": incident.incident_date,
        "affected_system": incident.affected_system,
        "impact_description": incident.impact_description,
        "severity_level": incident.severity_level,
        "actions_taken": incident.actions_taken,
        "status": incident.status,
        "attachments": incident.attachments,
        "reporter_id": incident.reporter_id,
        "assigned_to": incident.assigned_to,
        "created_at": incident.created_at,
        "updated_at": incident.updated_at,
        "reporter": {
            "id": incident.reporter.id,
            "username": incident.reporter.username,
            "email": incident.reporter.email
        } if incident.reporter else None,
        "assignee": {
            "id": incident.assignee.id,
            "username": incident.assignee.username,
            "email": incident.assignee.email
        } if incident.assignee else None
    }
    
    return incident_dict

@router.post("/", response_model=IncidentResponse)
async def create_incident(
    incident_data: IncidentCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create new incident."""
    # Check if user can create incidents
    if not current_user.can_create_incidents():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to create incidents"
        )
    
    # Validate assigned user if provided
    if incident_data.assigned_to:
        assigned_user = db.query(User).filter(User.id == incident_data.assigned_to).first()
        if not assigned_user or not assigned_user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Assigned user not found or inactive"
            )
    
    # Create incident
    db_incident = Incident(
        incident_title=incident_data.incident_title,
        incident_type=incident_data.incident_type,
        incident_date=incident_data.incident_date,
        affected_system=incident_data.affected_system,
        impact_description=incident_data.impact_description,
        severity_level=incident_data.severity_level,
        actions_taken=incident_data.actions_taken,
        status=incident_data.status,
        assigned_to=incident_data.assigned_to,
        reporter_id=current_user.id
    )
    
    db.add(db_incident)
    db.commit()
    db.refresh(db_incident)
    
    # Build response
    incident_dict = {
        "id": db_incident.id,
        "incident_title": db_incident.incident_title,
        "incident_type": db_incident.incident_type,
        "incident_date": db_incident.incident_date,
        "affected_system": db_incident.affected_system,
        "impact_description": db_incident.impact_description,
        "severity_level": db_incident.severity_level,
        "actions_taken": db_incident.actions_taken,
        "status": db_incident.status,
        "attachments": db_incident.attachments,
        "reporter_id": db_incident.reporter_id,
        "assigned_to": db_incident.assigned_to,
        "created_at": db_incident.created_at,
        "updated_at": db_incident.updated_at,
        "reporter": {
            "id": current_user.id,
            "username": current_user.username
        },
        "assignee": {
            "id": db_incident.assignee.id,
            "username": db_incident.assignee.username
        } if db_incident.assignee else None
    }
    
    return incident_dict

@router.put("/{incident_id}", response_model=IncidentResponse)
async def update_incident(
    incident_id: int,
    incident_data: IncidentUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update incident."""
    incident = db.query(Incident).filter(Incident.id == incident_id).first()
    if not incident:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Incident not found"
        )

    # Check permissions
    if not current_user.can_modify_incidents():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to modify incidents"
        )

    # Validate assigned user if being updated
    if incident_data.assigned_to is not None:
        if incident_data.assigned_to > 0:
            assigned_user = db.query(User).filter(User.id == incident_data.assigned_to).first()
            if not assigned_user or not assigned_user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Assigned user not found or inactive"
                )

    # Update incident fields
    update_data = incident_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(incident, field, value)

    db.commit()
    db.refresh(incident)

    # Build response
    incident_dict = {
        "id": incident.id,
        "incident_title": incident.incident_title,
        "incident_type": incident.incident_type,
        "incident_date": incident.incident_date,
        "affected_system": incident.affected_system,
        "impact_description": incident.impact_description,
        "severity_level": incident.severity_level,
        "actions_taken": incident.actions_taken,
        "status": incident.status,
        "attachments": incident.attachments,
        "reporter_id": incident.reporter_id,
        "assigned_to": incident.assigned_to,
        "created_at": incident.created_at,
        "updated_at": incident.updated_at,
        "reporter": {
            "id": incident.reporter.id,
            "username": incident.reporter.username
        } if incident.reporter else None,
        "assignee": {
            "id": incident.assignee.id,
            "username": incident.assignee.username
        } if incident.assignee else None
    }

    return incident_dict

@router.delete("/{incident_id}")
async def delete_incident(
    incident_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete incident (Admin only)."""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can delete incidents"
        )

    incident = db.query(Incident).filter(Incident.id == incident_id).first()
    if not incident:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Incident not found"
        )

    # Delete associated files if any
    if incident.attachments:
        try:
            attachments = json.loads(incident.attachments)
            for file_path in attachments:
                full_path = os.path.join(settings.UPLOAD_DIR, file_path)
                if os.path.exists(full_path):
                    os.remove(full_path)
        except:
            pass  # Continue with deletion even if file cleanup fails

    db.delete(incident)
    db.commit()

    return {"message": "Incident deleted successfully"}

@router.post("/{incident_id}/upload")
async def upload_attachment(
    incident_id: int,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Upload attachment to incident."""
    incident = db.query(Incident).filter(Incident.id == incident_id).first()
    if not incident:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Incident not found"
        )

    # Check permissions
    if not current_user.can_modify_incidents():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to modify incidents"
        )

    # Validate file
    if file.content_type not in settings.ALLOWED_FILE_TYPES:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File type not allowed"
        )

    # Check file size
    content = await file.read()
    if len(content) > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File size exceeds maximum allowed size"
        )

    # Generate unique filename
    file_extension = os.path.splitext(file.filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join(settings.UPLOAD_DIR, unique_filename)

    # Save file
    with open(file_path, "wb") as f:
        f.write(content)

    # Update incident attachments
    current_attachments = []
    if incident.attachments:
        try:
            current_attachments = json.loads(incident.attachments)
        except:
            current_attachments = []

    current_attachments.append(unique_filename)
    incident.attachments = json.dumps(current_attachments)

    db.commit()

    return {
        "message": "File uploaded successfully",
        "filename": file.filename,
        "file_id": unique_filename
    }

@router.get("/stats/dashboard", response_model=IncidentStats)
async def get_incident_statistics(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get incident statistics for dashboard."""
    # Total incidents
    total_incidents = db.query(Incident).count()

    # Open incidents
    open_incidents = db.query(Incident).filter(
        Incident.status.in_([IncidentStatus.OPEN, IncidentStatus.IN_PROGRESS, IncidentStatus.UNDER_INVESTIGATION])
    ).count()

    # Closed incidents
    closed_incidents = db.query(Incident).filter(Incident.status == IncidentStatus.CLOSED).count()

    # Critical incidents
    critical_incidents = db.query(Incident).filter(Incident.severity_level == SeverityLevel.CRITICAL).count()

    # High priority incidents
    high_priority_incidents = db.query(Incident).filter(
        Incident.severity_level.in_([SeverityLevel.CRITICAL, SeverityLevel.HIGH])
    ).count()

    # Incidents by type
    incidents_by_type = {}
    for incident_type in IncidentType:
        count = db.query(Incident).filter(Incident.incident_type == incident_type).count()
        incidents_by_type[incident_type.value] = count

    # Incidents by status
    incidents_by_status = {}
    for status in IncidentStatus:
        count = db.query(Incident).filter(Incident.status == status).count()
        incidents_by_status[status.value] = count

    # Incidents by severity
    incidents_by_severity = {}
    for severity in SeverityLevel:
        count = db.query(Incident).filter(Incident.severity_level == severity).count()
        incidents_by_severity[severity.value] = count

    return {
        "total_incidents": total_incidents,
        "open_incidents": open_incidents,
        "closed_incidents": closed_incidents,
        "critical_incidents": critical_incidents,
        "high_priority_incidents": high_priority_incidents,
        "incidents_by_type": incidents_by_type,
        "incidents_by_status": incidents_by_status,
        "incidents_by_severity": incidents_by_severity
    }

@router.get("/config/types")
async def get_incident_types(current_user: User = Depends(get_current_user)):
    """Get available incident types."""
    return {
        "incident_types": [
            {"value": incident_type.name, "label": incident_type.value}
            for incident_type in IncidentType
        ]
    }

@router.get("/config/severities")
async def get_severity_levels(current_user: User = Depends(get_current_user)):
    """Get available severity levels."""
    return {
        "severity_levels": [
            {"value": severity.name, "label": severity.value}
            for severity in SeverityLevel
        ]
    }

@router.get("/config/statuses")
async def get_incident_statuses(current_user: User = Depends(get_current_user)):
    """Get available incident statuses."""
    return {
        "statuses": [
            {"value": status.name, "label": status.value}
            for status in IncidentStatus
        ]
    }
