import React from 'react';
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/solid';

interface StatsCardProps {
  title: string;
  value: number;
  icon: React.ComponentType<any>;
  color: 'blue' | 'red' | 'yellow' | 'green' | 'purple';
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, icon: Icon, color, trend }) => {
  const colorClasses = {
    blue: 'bg-blue-600 text-blue-100',
    red: 'bg-red-600 text-red-100',
    yellow: 'bg-yellow-600 text-yellow-100',
    green: 'bg-green-600 text-green-100',
    purple: 'bg-purple-600 text-purple-100',
  };

  return (
    <div className="bg-dark-800 border border-dark-700 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:border-dark-600">
      <div className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center justify-between mb-3">
              <div className={`p-3 rounded-xl ${colorClasses[color]} shadow-lg`}>
                <Icon className="h-7 w-7" />
              </div>
              {trend && (
                <div className={`flex items-center text-sm font-medium ${
                  trend.isPositive ? 'text-green-400' : 'text-red-400'
                }`}>
                  {trend.isPositive ? (
                    <ArrowUpIcon className="h-4 w-4 ml-1" />
                  ) : (
                    <ArrowDownIcon className="h-4 w-4 ml-1" />
                  )}
                  {Math.abs(trend.value)}%
                </div>
              )}
            </div>
            <div className="text-center">
              <p className="text-3xl font-bold text-gray-100 mb-2 tracking-tight">
                {value.toLocaleString('ar-SA')}
              </p>
              <p className="text-sm font-medium text-gray-400 leading-relaxed">
                {title}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatsCard;
