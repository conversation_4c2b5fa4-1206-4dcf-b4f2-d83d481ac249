import React from 'react';
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/solid';

interface StatsCardProps {
  title: string;
  value: number;
  icon: React.ComponentType<any>;
  color: 'blue' | 'red' | 'yellow' | 'green' | 'purple';
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, icon: Icon, color, trend }) => {
  const colorClasses = {
    blue: 'bg-blue-600 text-blue-100',
    red: 'bg-red-600 text-red-100',
    yellow: 'bg-yellow-600 text-yellow-100',
    green: 'bg-green-600 text-green-100',
    purple: 'bg-purple-600 text-purple-100',
  };

  return (
    <div className="bg-dark-800 border border-dark-700 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:border-dark-600 hover:bg-slate-700">
      <div className="p-6">
        <div className="flex items-center justify-center">
          <div className="text-center w-full">
            {/* Icon at top */}
            <div className="flex justify-center mb-4">
              <div className={`p-3 rounded-xl ${colorClasses[color]} shadow-lg`}>
                <Icon className="h-6 w-6" />
              </div>
            </div>

            {/* Number in center */}
            <div className="mb-3">
              <p className="stat-number text-gray-100">
                {value.toLocaleString('ar-SA')}
              </p>
            </div>

            {/* Label at bottom */}
            <div className="mb-2">
              <p className="stat-label">
                {title}
              </p>
            </div>

            {/* Trend indicator */}
            {trend && (
              <div className={`flex items-center justify-center text-xs font-medium ${
                trend.isPositive ? 'text-green-400' : 'text-red-400'
              }`}>
                {trend.isPositive ? (
                  <ArrowUpIcon className="h-3 w-3 ml-1" />
                ) : (
                  <ArrowDownIcon className="h-3 w-3 ml-1" />
                )}
                {Math.abs(trend.value)}%
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatsCard;
