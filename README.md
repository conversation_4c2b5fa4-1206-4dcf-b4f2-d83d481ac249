# Cybersecurity Incident Reporting System

A comprehensive web-based cybersecurity incident reporting and user management system compliant with National Cybersecurity Authority (NCA) standards and ISO 27001.

## Features

- **Authentication System**: JWT-based authentication with bcrypt password hashing
- **User Management**: Role-based access control (<PERSON><PERSON>, Analyst, Viewer)
- **Incident Management**: Complete incident lifecycle management
- **Arabic Language Support**: Full RTL layout support
- **Professional Dark Theme**: Modern responsive UI
- **Security Compliant**: NCA standards and ISO 27001 compliant

## Tech Stack

- **Frontend**: React + Next.js + TailwindCSS
- **Backend**: Python FastAPI
- **Database**: SQLite
- **Security**: JWT + bcrypt
- **Language**: Arabic (RTL support)

## Project Structure

```
IR-Platform/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core configurations
│   │   ├── models/         # Database models
│   │   ├── schemas/        # Pydantic schemas
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utility functions
│   ├── database.db         # SQLite database
│   ├── requirements.txt    # Python dependencies
│   └── main.py            # FastAPI application entry
├── frontend/               # Next.js frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/         # Next.js pages
│   │   ├── contexts/      # React contexts
│   │   ├── utils/         # Utility functions
│   │   └── styles/        # CSS styles
│   ├── package.json       # Node.js dependencies
│   └── next.config.js     # Next.js configuration
└── docs/                  # Documentation
```

## Quick Start

### Prerequisites

- Python 3.8+
- Node.js 16+
- npm or yarn

### Backend Setup

1. Navigate to backend directory:
```bash
cd backend
```

2. Create virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Initialize database:
```bash
python init_db.py
```

5. Start the server:
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend Setup

1. Navigate to frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Start development server:
```bash
npm run dev
```

## Default Admin User

For testing purposes, a default admin user is created:
- **Username**: `admin`
- **Password**: `admin123`
- **Role**: Admin

## API Documentation

Once the backend is running, visit:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Database Schema

### Users Table
- `id`: Primary key (auto-increment)
- `username`: Unique username
- `hashed_password`: bcrypt hashed password
- `email`: Optional email address
- `role`: User role (admin/analyst/viewer)
- `created_at`: Account creation timestamp
- `last_login`: Last login timestamp
- `is_active`: Account status

### Incidents Table
- `id`: Primary key (auto-increment)
- `incident_title`: Incident title
- `incident_type`: Type of incident
- `incident_date`: When incident occurred
- `affected_system`: System affected
- `impact_description`: Description of impact
- `severity_level`: Critical/High/Medium/Low
- `actions_taken`: Actions taken to resolve
- `status`: Open/Closed/In Progress/Under Investigation
- `attachments`: File attachments (optional)
- `reporter_id`: Foreign key to users
- `assigned_to`: Foreign key to users (optional)
- `created_at`: Report creation timestamp
- `updated_at`: Last update timestamp

## Security Features

- Password complexity validation
- Rate limiting for login attempts
- CORS configuration
- Input sanitization
- SQL injection prevention
- File upload security
- JWT token expiration and refresh

## Role-Based Access Control

- **Admin**: Full system access, user management, all incident operations
- **Analyst**: Create and modify incidents, limited user access
- **Viewer**: Read-only access to incidents

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
