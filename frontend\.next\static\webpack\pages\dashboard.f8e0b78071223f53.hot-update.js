"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "./src/components/dashboard/StatsCard.tsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/StatsCard.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon!=!@heroicons/react/24/solid */ \"__barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon!=!./node_modules/@heroicons/react/24/solid/esm/index.js\");\n\n\n\nconst StatsCard = (param)=>{\n    let { title, value, icon: Icon, color, trend } = param;\n    const colorClasses = {\n        blue: \"bg-blue-600 text-blue-100\",\n        red: \"bg-red-600 text-red-100\",\n        yellow: \"bg-yellow-600 text-yellow-100\",\n        green: \"bg-green-600 text-green-100\",\n        purple: \"bg-purple-600 text-purple-100\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-dark-800 border border-dark-700 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:border-dark-600 hover:bg-slate-700\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 rounded-xl \".concat(colorClasses[color], \" shadow-lg\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"stat-number text-gray-100\",\n                                children: value.toLocaleString(\"ar-SA\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"stat-label\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 13\n                        }, undefined),\n                        trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center text-xs font-medium \".concat(trend.isPositive ? \"text-green-400\" : \"text-red-400\"),\n                            children: [\n                                trend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__.ArrowUpIcon, {\n                                    className: \"h-3 w-3 ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 19\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__.ArrowDownIcon, {\n                                    className: \"h-3 w-3 ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 19\n                                }, undefined),\n                                Math.abs(trend.value),\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n_c = StatsCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (StatsCard);\nvar _c;\n$RefreshReg$(_c, \"StatsCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/dashboard/StatsCard.tsx\n"));

/***/ })

});