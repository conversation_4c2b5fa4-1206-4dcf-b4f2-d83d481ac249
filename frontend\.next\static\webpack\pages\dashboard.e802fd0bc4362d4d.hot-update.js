"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "./src/components/layout/Layout.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Layout.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,ChartBarIcon,Cog6ToothIcon,HomeIcon,ShieldCheckIcon,UserCircleIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,ChartBarIcon,Cog6ToothIcon,HomeIcon,ShieldCheckIcon,UserCircleIcon,UsersIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst Layout = (param)=>{\n    let { children } = param;\n    _s();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout, hasRole } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const navigation = [\n        {\n            name: \"لوحة التحكم\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.HomeIcon,\n            current: router.pathname === \"/dashboard\"\n        },\n        {\n            name: \"الحوادث الأمنية\",\n            href: \"/incidents\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShieldCheckIcon,\n            current: router.pathname.startsWith(\"/incidents\")\n        },\n        {\n            name: \"إدارة المستخدمين\",\n            href: \"/users\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.UsersIcon,\n            current: router.pathname.startsWith(\"/users\"),\n            requiredRoles: [\n                \"admin\"\n            ]\n        },\n        {\n            name: \"التقارير\",\n            href: \"/reports\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChartBarIcon,\n            current: router.pathname.startsWith(\"/reports\")\n        },\n        {\n            name: \"الإعدادات\",\n            href: \"/settings\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.Cog6ToothIcon,\n            current: router.pathname.startsWith(\"/settings\")\n        }\n    ];\n    const filteredNavigation = navigation.filter((item)=>!item.requiredRoles || hasRole(item.requiredRoles));\n    const handleLogout = ()=>{\n        logout();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-dark-950\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 lg:hidden \".concat(sidebarOpen ? \"block\" : \"hidden\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-y-0 right-0 w-64 bg-dark-900 shadow-xl border-l border-dark-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between h-20 px-6 border-b border-dark-700 bg-dark-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-primary-600 p-2 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShieldCheckIcon, {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-bold text-white\",\n                                                        children: \"نظام الحوادث الأمنية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: \"إدارة الأمن السيبراني\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSidebarOpen(false),\n                                        className: \"text-gray-400 hover:text-white transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.XMarkIcon, {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"mt-6 px-4 space-y-2\",\n                                children: filteredNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: item.href,\n                                        className: \"sidebar-link \".concat(item.current ? \"active\" : \"\"),\n                                        onClick: ()=>setSidebarOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"h-5 w-5 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            item.name\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:right-0 lg:w-64 lg:block\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full bg-dark-900 border-l border-dark-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center h-20 px-6 border-b border-dark-700 bg-dark-800\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-primary-600 p-2 rounded-lg shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShieldCheckIcon, {\n                                            className: \"h-7 w-7 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-bold text-white\",\n                                                children: \"نظام الحوادث الأمنية\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"إدارة الأمن السيبراني\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 mt-6 px-4 space-y-2\",\n                            children: filteredNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: item.href,\n                                    className: \"sidebar-link \".concat(item.current ? \"active\" : \"\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"h-5 w-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-dark-700 p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.UserCircleIcon, {\n                                            className: \"h-8 w-8 text-gray-400 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-white\",\n                                                    children: user === null || user === void 0 ? void 0 : user.username\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: (user === null || user === void 0 ? void 0 : user.role) === \"admin\" ? \"مدير\" : (user === null || user === void 0 ? void 0 : user.role) === \"analyst\" ? \"محلل أمني\" : \"مشاهد\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogout,\n                                    className: \"flex items-center w-full px-3 py-2 text-sm text-gray-300 hover:bg-dark-700 hover:text-white rounded-lg transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ArrowRightOnRectangleIcon, {\n                                            className: \"h-5 w-5 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"تسجيل الخروج\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:mr-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-40 bg-dark-900 border-b border-dark-700 lg:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-lg font-semibold text-white\",\n                                    children: \"نظام الحوادث الأمنية\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(true),\n                                    className: \"text-gray-400 hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.Bars3Icon, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Layout, \"DHl2WrQkRhUiGs6dBiw/mybn0qg=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Layout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Layout);\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layout/Layout.tsx\n"));

/***/ })

});