import React from 'react';
import { 
  UserCircleIcon, 
  BellIcon, 
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  ClockIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';

const TopBar: React.FC = () => {
  const { user, logout } = useAuth();

  const getCurrentDateTime = () => {
    const now = new Date();
    const arabicDate = now.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    });
    const arabicTime = now.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
    return `${arabicDate} - ${arabicTime}`;
  };

  const getRoleDisplayName = (role: string) => {
    const roleMap: Record<string, string> = {
      'admin': 'مدير النظام',
      'analyst': 'محلل أمني',
      'viewer': 'مشاهد'
    };
    return roleMap[role] || role;
  };

  return (
    <div className="bg-dark-800 border-b border-dark-700 shadow-lg hidden lg:block">
      <div className="max-w-7xl mx-auto px-4 md:px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo and Company Info */}
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="bg-primary-600 p-2 rounded-lg shadow-lg">
              <ShieldCheckIcon className="h-6 w-6 md:h-8 md:w-8 text-white" />
            </div>
            <div className="hidden md:block">
              <h1 className="text-lg md:text-xl font-bold text-gray-100">
                مؤسسة الأمن السيبراني
              </h1>
              <p className="text-sm text-gray-400">
                نظام إدارة الحوادث الأمنية المتقدم
              </p>
            </div>
          </div>

          {/* Center - Current Time */}
          <div className="hidden md:flex items-center space-x-2 space-x-reverse bg-dark-700 px-4 py-2 rounded-lg">
            <ClockIcon className="h-5 w-5 text-gray-400" />
            <div className="text-center">
              <p className="text-sm font-medium text-gray-200">
                {getCurrentDateTime()}
              </p>
              <p className="text-xs text-gray-400">آخر تحديث</p>
            </div>
          </div>

          {/* Right - User Info and Actions */}
          <div className="flex items-center space-x-2 md:space-x-4 space-x-reverse">
            {/* Notifications */}
            <button className="relative p-2 text-gray-400 hover:text-gray-200 hover:bg-dark-700 rounded-lg transition-colors">
              <BellIcon className="h-5 w-5 md:h-6 md:w-6" />
              <span className="absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-500"></span>
            </button>

            {/* Settings */}
            <button className="hidden md:block p-2 text-gray-400 hover:text-gray-200 hover:bg-dark-700 rounded-lg transition-colors">
              <Cog6ToothIcon className="h-6 w-6" />
            </button>

            {/* User Profile */}
            <div className="flex items-center space-x-2 md:space-x-3 space-x-reverse bg-dark-700 px-2 md:px-4 py-2 rounded-lg">
              <div className="text-right hidden md:block">
                <p className="text-sm font-medium text-gray-200">
                  {user?.username || 'مستخدم'}
                </p>
                <p className="text-xs text-gray-400">
                  {getRoleDisplayName(user?.role || 'viewer')}
                </p>
              </div>
              <div className="h-8 w-8 bg-primary-600 rounded-full flex items-center justify-center">
                <UserCircleIcon className="h-5 w-5 text-white" />
              </div>
            </div>

            {/* Logout */}
            <button
              onClick={logout}
              className="p-2 text-gray-400 hover:text-red-400 hover:bg-dark-700 rounded-lg transition-colors"
              title="تسجيل الخروج"
            >
              <ArrowRightOnRectangleIcon className="h-5 w-5 md:h-6 md:w-6" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopBar;
