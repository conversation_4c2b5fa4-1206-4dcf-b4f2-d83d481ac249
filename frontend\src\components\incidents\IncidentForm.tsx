import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useForm } from 'react-hook-form';
import DatePicker from 'react-datepicker';
import { ArrowRightIcon, CloudArrowUpIcon } from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import apiClient from '@/utils/api';
import { IncidentCreate, IncidentUpdate, Incident, User } from '@/types';
import toast from 'react-hot-toast';
import "react-datepicker/dist/react-datepicker.css";

interface IncidentFormProps {
  incident?: Incident;
  isEdit?: boolean;
}

interface FormData {
  incident_title: string;
  incident_type: string;
  incident_date: Date;
  affected_system: string;
  impact_description: string;
  severity_level: string;
  actions_taken?: string;
  status: string;
  assigned_to?: number;
}

const IncidentForm: React.FC<IncidentFormProps> = ({ incident, isEdit = false }) => {
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const { user, hasRole } = useAuth();
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    control,
  } = useForm<FormData>({
    defaultValues: {
      incident_date: incident ? new Date(incident.incident_date) : new Date(),
      status: incident?.status || 'OPEN',
      severity_level: incident?.severity_level || 'MEDIUM',
      incident_type: incident?.incident_type || 'OTHER',
    }
  });

  const watchedDate = watch('incident_date');

  useEffect(() => {
    if (hasRole(['admin', 'analyst'])) {
      fetchUsers();
    }
    
    if (incident) {
      setValue('incident_title', incident.incident_title);
      setValue('incident_type', incident.incident_type);
      setValue('incident_date', new Date(incident.incident_date));
      setValue('affected_system', incident.affected_system);
      setValue('impact_description', incident.impact_description);
      setValue('severity_level', incident.severity_level);
      setValue('actions_taken', incident.actions_taken || '');
      setValue('status', incident.status);
      setValue('assigned_to', incident.assigned_to || undefined);
    }
  }, [incident, setValue, hasRole]);

  const fetchUsers = async () => {
    try {
      const data = await apiClient.get<User[]>('/users/');
      setUsers(data.filter(u => u.is_active));
    } catch (error) {
      console.error('Failed to fetch users:', error);
    }
  };

  const onSubmit = async (data: FormData) => {
    try {
      setLoading(true);
      
      const formattedData = {
        ...data,
        incident_date: data.incident_date.toISOString(),
        assigned_to: data.assigned_to || undefined,
      };

      let savedIncident: Incident;

      if (isEdit && incident) {
        savedIncident = await apiClient.put<Incident>(`/incidents/${incident.id}`, formattedData);
        toast.success('تم تحديث الحادث بنجاح');
      } else {
        savedIncident = await apiClient.post<Incident>('/incidents/', formattedData);
        toast.success('تم إنشاء الحادث بنجاح');
      }

      // Upload files if any
      if (uploadedFiles.length > 0) {
        for (const file of uploadedFiles) {
          try {
            await apiClient.uploadFile(`/incidents/${savedIncident.id}/upload`, file);
          } catch (error) {
            console.error('File upload error:', error);
            toast.error(`فشل في رفع الملف: ${file.name}`);
          }
        }
      }

      router.push('/incidents');
    } catch (error: any) {
      toast.error(error.detail || 'فشل في حفظ الحادث');
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      setUploadedFiles(prev => [...prev, ...files]);
    }
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center mb-4">
            <button
              onClick={() => router.back()}
              className="btn-secondary flex items-center ml-4"
            >
              <ArrowRightIcon className="h-5 w-5 ml-2" />
              العودة
            </button>
            <h1 className="text-2xl font-bold text-gray-100">
              {isEdit ? 'تعديل الحادث' : 'إضافة حادث جديد'}
            </h1>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-100">معلومات الحادث الأساسية</h3>
            </div>
            <div className="card-body space-y-6">
              {/* Incident Title */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  عنوان الحادث *
                </label>
                <input
                  {...register('incident_title', {
                    required: 'عنوان الحادث مطلوب',
                    minLength: { value: 5, message: 'العنوان يجب أن يكون 5 أحرف على الأقل' }
                  })}
                  type="text"
                  className="input-field w-full"
                  placeholder="أدخل عنوان الحادث"
                />
                {errors.incident_title && (
                  <p className="form-error">{errors.incident_title.message}</p>
                )}
              </div>

              {/* Incident Type and Date */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    نوع الحادث *
                  </label>
                  <select
                    {...register('incident_type', { required: 'نوع الحادث مطلوب' })}
                    className="input-field w-full"
                  >
                    <option value="MALWARE_INFECTION">إصابة بالبرمجيات الخبيثة</option>
                    <option value="PHISHING_ATTACK">هجوم تصيد</option>
                    <option value="DATA_BREACH">تسريب بيانات</option>
                    <option value="UNAUTHORIZED_ACCESS">وصول غير مصرح</option>
                    <option value="DDOS_ATTACK">هجوم حجب الخدمة</option>
                    <option value="INSIDER_THREAT">تهديد داخلي</option>
                    <option value="SYSTEM_COMPROMISE">اختراق النظام</option>
                    <option value="NETWORK_INTRUSION">تسلل الشبكة</option>
                    <option value="SOCIAL_ENGINEERING">هندسة اجتماعية</option>
                    <option value="PHYSICAL_SECURITY_BREACH">خرق الأمان المادي</option>
                    <option value="OTHER">أخرى</option>
                  </select>
                  {errors.incident_type && (
                    <p className="form-error">{errors.incident_type.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    تاريخ الحادث *
                  </label>
                  <DatePicker
                    selected={watchedDate}
                    onChange={(date) => setValue('incident_date', date || new Date())}
                    showTimeSelect
                    timeFormat="HH:mm"
                    timeIntervals={15}
                    dateFormat="yyyy/MM/dd HH:mm"
                    className="input-field w-full"
                    placeholderText="اختر تاريخ ووقت الحادث"
                    maxDate={new Date()}
                  />
                  {errors.incident_date && (
                    <p className="form-error">{errors.incident_date.message}</p>
                  )}
                </div>
              </div>

              {/* Affected System */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  النظام المتأثر *
                </label>
                <input
                  {...register('affected_system', {
                    required: 'النظام المتأثر مطلوب',
                    minLength: { value: 2, message: 'اسم النظام يجب أن يكون حرفين على الأقل' }
                  })}
                  type="text"
                  className="input-field w-full"
                  placeholder="مثال: خادم البريد الإلكتروني، محطة العمل، قاعدة البيانات"
                />
                {errors.affected_system && (
                  <p className="form-error">{errors.affected_system.message}</p>
                )}
              </div>

              {/* Impact Description */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  وصف التأثير *
                </label>
                <textarea
                  {...register('impact_description', {
                    required: 'وصف التأثير مطلوب',
                    minLength: { value: 10, message: 'الوصف يجب أن يكون 10 أحرف على الأقل' }
                  })}
                  rows={4}
                  className="input-field w-full"
                  placeholder="اشرح تأثير الحادث على النظام والعمليات..."
                />
                {errors.impact_description && (
                  <p className="form-error">{errors.impact_description.message}</p>
                )}
              </div>

              {/* Severity and Status */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    مستوى الخطورة *
                  </label>
                  <select
                    {...register('severity_level', { required: 'مستوى الخطورة مطلوب' })}
                    className="input-field w-full"
                  >
                    <option value="LOW">منخفض</option>
                    <option value="MEDIUM">متوسط</option>
                    <option value="HIGH">عالي</option>
                    <option value="CRITICAL">حرج</option>
                  </select>
                  {errors.severity_level && (
                    <p className="form-error">{errors.severity_level.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    حالة الحادث
                  </label>
                  <select
                    {...register('status')}
                    className="input-field w-full"
                  >
                    <option value="OPEN">مفتوح</option>
                    <option value="IN_PROGRESS">قيد المعالجة</option>
                    <option value="UNDER_INVESTIGATION">قيد التحقيق</option>
                    <option value="CLOSED">مغلق</option>
                  </select>
                </div>
              </div>

              {/* Actions Taken */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  الإجراءات المتخذة
                </label>
                <textarea
                  {...register('actions_taken')}
                  rows={3}
                  className="input-field w-full"
                  placeholder="اشرح الإجراءات التي تم اتخاذها للتعامل مع الحادث..."
                />
              </div>

              {/* Assigned To */}
              {hasRole(['admin', 'analyst']) && users.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    مُكلف إلى
                  </label>
                  <select
                    {...register('assigned_to')}
                    className="input-field w-full"
                  >
                    <option value="">غير مُكلف</option>
                    {users.map((u) => (
                      <option key={u.id} value={u.id}>
                        {u.username} ({u.role === 'admin' ? 'مدير' : u.role === 'analyst' ? 'محلل' : 'مشاهد'})
                      </option>
                    ))}
                  </select>
                </div>
              )}
            </div>
          </div>

          {/* File Upload */}
          {!isEdit && (
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-gray-100">المرفقات</h3>
              </div>
              <div className="card-body">
                <div className="border-2 border-dashed border-dark-600 rounded-lg p-6">
                  <div className="text-center">
                    <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="mt-4">
                      <label htmlFor="file-upload" className="cursor-pointer">
                        <span className="mt-2 block text-sm font-medium text-gray-300">
                          اختر الملفات أو اسحبها هنا
                        </span>
                        <input
                          id="file-upload"
                          name="file-upload"
                          type="file"
                          multiple
                          className="sr-only"
                          onChange={handleFileChange}
                          accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.txt"
                        />
                      </label>
                      <p className="mt-1 text-xs text-gray-400">
                        PDF, DOC, XLS, صور (حتى 10MB لكل ملف)
                      </p>
                    </div>
                  </div>
                </div>

                {uploadedFiles.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-300 mb-2">الملفات المحددة:</h4>
                    <div className="space-y-2">
                      {uploadedFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between bg-dark-700 p-2 rounded">
                          <span className="text-sm text-gray-300">{file.name}</span>
                          <button
                            type="button"
                            onClick={() => removeFile(index)}
                            className="text-red-400 hover:text-red-300 text-sm"
                          >
                            إزالة
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex justify-end space-x-4 space-x-reverse">
            <button
              type="button"
              onClick={() => router.back()}
              className="btn-secondary"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={loading}
              className="btn-primary flex items-center"
            >
              {loading ? (
                <>
                  <div className="spinner mr-2"></div>
                  جاري الحفظ...
                </>
              ) : (
                isEdit ? 'تحديث الحادث' : 'إنشاء الحادث'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default IncidentForm;
