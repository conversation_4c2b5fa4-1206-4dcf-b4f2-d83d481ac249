import re
from typing import Optional
from fastapi import HTT<PERSON>Ex<PERSON>, status

def validate_email(email: str) -> bool:
    """Validate email format."""
    if not email:
        return True  # Email is optional
    
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(email_pattern, email) is not None

def sanitize_input(input_string: str) -> str:
    """Sanitize input string to prevent XSS and injection attacks."""
    if not input_string:
        return ""
    
    # Remove potentially dangerous characters
    dangerous_chars = ['<', '>', '"', "'", '&', '\x00', '\n', '\r', '\t']
    sanitized = input_string
    
    for char in dangerous_chars:
        sanitized = sanitized.replace(char, '')
    
    return sanitized.strip()

def validate_file_type(content_type: str, allowed_types: list) -> bool:
    """Validate file content type."""
    return content_type in allowed_types

def validate_file_size(file_size: int, max_size: int) -> bool:
    """Validate file size."""
    return file_size <= max_size

def validate_username(username: str) -> bool:
    """Validate username format."""
    if not username:
        return False
    
    # Username should be 3-50 characters, alphanumeric and underscores only
    username_pattern = r'^[a-zA-Z0-9_]{3,50}$'
    return re.match(username_pattern, username) is not None

def validate_incident_title(title: str) -> bool:
    """Validate incident title."""
    if not title or len(title.strip()) < 5:
        return False
    return len(title) <= 200

def validate_system_name(system_name: str) -> bool:
    """Validate affected system name."""
    if not system_name or len(system_name.strip()) < 2:
        return False
    return len(system_name) <= 200

def validate_description(description: str) -> bool:
    """Validate description fields."""
    if not description or len(description.strip()) < 10:
        return False
    return True
