import React, { useEffect, useState } from 'react';
import {
  ExclamationTriangleIcon,
  ShieldExclamationIcon,
  CheckCircleIcon,
  ClockIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import { <PERSON><PERSON>hart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import StatsCard from './StatsCard';
import apiClient from '@/utils/api';
import { IncidentStats, Incident } from '@/types';
import toast from 'react-hot-toast';

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<IncidentStats | null>(null);
  const [recentIncidents, setRecentIncidents] = useState<Incident[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const [statsData, incidentsData] = await Promise.all([
        apiClient.get<IncidentStats>('/incidents/stats/dashboard/'),
        apiClient.get<Incident[]>('/incidents/?limit=5')
      ]);
      
      setStats(statsData);
      setRecentIncidents(incidentsData);
    } catch (error: any) {
      toast.error('فشل في تحميل بيانات لوحة التحكم');
      console.error('Dashboard data fetch error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical': return '#dc2626';
      case 'high': return '#ea580c';
      case 'medium': return '#d97706';
      case 'low': return '#16a34a';
      default: return '#6b7280';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'open': return '#dc2626';
      case 'in_progress': return '#d97706';
      case 'under_investigation': return '#2563eb';
      case 'closed': return '#16a34a';
      default: return '#6b7280';
    }
  };

  const formatIncidentType = (type: string) => {
    const typeMap: Record<string, string> = {
      'MALWARE_INFECTION': 'إصابة بالبرمجيات الخبيثة',
      'PHISHING_ATTACK': 'هجوم تصيد',
      'DATA_BREACH': 'تسريب بيانات',
      'UNAUTHORIZED_ACCESS': 'وصول غير مصرح',
      'DDOS_ATTACK': 'هجوم حجب الخدمة',
      'INSIDER_THREAT': 'تهديد داخلي',
      'SYSTEM_COMPROMISE': 'اختراق النظام',
      'NETWORK_INTRUSION': 'تسلل الشبكة',
      'SOCIAL_ENGINEERING': 'هندسة اجتماعية',
      'PHYSICAL_SECURITY_BREACH': 'خرق الأمان المادي',
      'OTHER': 'أخرى'
    };
    return typeMap[type] || type;
  };

  const formatStatus = (status: string) => {
    const statusMap: Record<string, string> = {
      'OPEN': 'مفتوح',
      'IN_PROGRESS': 'قيد المعالجة',
      'UNDER_INVESTIGATION': 'قيد التحقيق',
      'CLOSED': 'مغلق'
    };
    return statusMap[status] || status;
  };

  const formatSeverity = (severity: string) => {
    const severityMap: Record<string, string> = {
      'CRITICAL': 'حرج',
      'HIGH': 'عالي',
      'MEDIUM': 'متوسط',
      'LOW': 'منخفض'
    };
    return severityMap[severity] || severity;
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="card">
                <div className="card-body">
                  <div className="h-16 bg-dark-700 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="p-6">
        <div className="text-center text-gray-400">
          فشل في تحميل بيانات لوحة التحكم
        </div>
      </div>
    );
  }

  // Prepare chart data
  const severityData = Object.entries(stats.incidents_by_severity).map(([key, value]) => ({
    name: formatSeverity(key),
    value,
    color: getSeverityColor(key)
  }));

  const statusData = Object.entries(stats.incidents_by_status).map(([key, value]) => ({
    name: formatStatus(key),
    value,
    color: getStatusColor(key)
  }));

  const typeData = Object.entries(stats.incidents_by_type)
    .filter(([_, value]) => value > 0)
    .map(([key, value]) => ({
      name: formatIncidentType(key),
      value
    }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 6);

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-100">لوحة التحكم</h1>
        <p className="text-gray-400">نظرة عامة على الحوادث الأمنية</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="إجمالي الحوادث"
          value={stats.total_incidents}
          icon={ExclamationTriangleIcon}
          color="blue"
        />
        <StatsCard
          title="الحوادث المفتوحة"
          value={stats.open_incidents}
          icon={ClockIcon}
          color="red"
        />
        <StatsCard
          title="الحوادث الحرجة"
          value={stats.critical_incidents}
          icon={ShieldExclamationIcon}
          color="purple"
        />
        <StatsCard
          title="الحوادث المغلقة"
          value={stats.closed_incidents}
          icon={CheckCircleIcon}
          color="green"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Severity Distribution */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-100">توزيع الحوادث حسب الخطورة</h3>
          </div>
          <div className="card-body">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={severityData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {severityData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Status Distribution */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-100">توزيع الحوادث حسب الحالة</h3>
          </div>
          <div className="card-body">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={statusData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {statusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Incident Types Chart */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-100">أنواع الحوادث الأكثر شيوعاً</h3>
        </div>
        <div className="card-body">
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={typeData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis 
                dataKey="name" 
                stroke="#9CA3AF"
                fontSize={12}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis stroke="#9CA3AF" />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: '#1F2937', 
                  border: '1px solid #374151',
                  borderRadius: '8px'
                }}
              />
              <Bar dataKey="value" fill="#3B82F6" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Recent Incidents */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-100">الحوادث الأخيرة</h3>
        </div>
        <div className="card-body">
          {recentIncidents.length > 0 ? (
            <div className="space-y-4">
              {recentIncidents.map((incident) => (
                <div key={incident.id} className="flex items-center justify-between p-4 bg-dark-700 rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-100">{incident.incident_title}</h4>
                    <p className="text-sm text-gray-400">
                      {formatIncidentType(incident.incident_type)} • {incident.affected_system}
                    </p>
                  </div>
                  <div className="flex items-center space-x-4 space-x-reverse">
                    <span className={`badge badge-${incident.severity_level.toLowerCase()}`}>
                      {formatSeverity(incident.severity_level)}
                    </span>
                    <span className={`badge badge-${incident.status.toLowerCase().replace('_', '-')}`}>
                      {formatStatus(incident.status)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center text-gray-400 py-8">
              لا توجد حوادث حديثة
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
