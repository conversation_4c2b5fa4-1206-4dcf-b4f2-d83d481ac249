import React, { useEffect, useState } from 'react';
import {
  ExclamationTriangleIcon,
  ShieldExclamationIcon,
  CheckCircleIcon,
  ClockIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  ExclamationCircleIcon,
  FireIcon,
} from '@heroicons/react/24/outline';
import { <PERSON>Chart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import StatsCard from './StatsCard';
import RecentIncidentsTable from './RecentIncidentsTable';
import apiClient from '@/utils/api';
import { IncidentStats, Incident } from '@/types';
import toast from 'react-hot-toast';

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<IncidentStats | null>(null);
  const [recentIncidents, setRecentIncidents] = useState<Incident[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const [statsData, incidentsData] = await Promise.all([
        apiClient.get<IncidentStats>('/incidents/stats/dashboard/'),
        apiClient.get<Incident[]>('/incidents/?limit=5')
      ]);
      
      setStats(statsData);
      setRecentIncidents(incidentsData);
    } catch (error: any) {
      toast.error('فشل في تحميل بيانات لوحة التحكم');
      console.error('Dashboard data fetch error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical': return '#ef4444'; // أحمر فاتح للحرج
      case 'high': return '#f97316';     // برتقالي فاتح للعالي
      case 'medium': return '#eab308';   // أصفر فاتح للمتوسط
      case 'low': return '#22c55e';      // أخضر فاتح للمنخفض
      default: return '#94a3b8';         // رمادي فاتح
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'open': return '#ef4444';           // أحمر فاتح للمفتوح
      case 'in_progress': return '#f59e0b';    // برتقالي فاتح لقيد المعالجة
      case 'under_investigation': return '#3b82f6'; // أزرق فاتح لقيد التحقيق
      case 'closed': return '#10b981';         // أخضر فاتح للمغلق
      default: return '#94a3b8';               // رمادي فاتح
    }
  };

  const formatIncidentType = (type: string) => {
    const typeMap: Record<string, string> = {
      'MALWARE_INFECTION': 'إصابة بالبرمجيات الخبيثة',
      'PHISHING_ATTACK': 'هجوم تصيد',
      'DATA_BREACH': 'تسريب بيانات',
      'UNAUTHORIZED_ACCESS': 'وصول غير مصرح',
      'DDOS_ATTACK': 'هجوم حجب الخدمة',
      'INSIDER_THREAT': 'تهديد داخلي',
      'SYSTEM_COMPROMISE': 'اختراق النظام',
      'NETWORK_INTRUSION': 'تسلل الشبكة',
      'SOCIAL_ENGINEERING': 'هندسة اجتماعية',
      'PHYSICAL_SECURITY_BREACH': 'خرق الأمان المادي',
      'OTHER': 'أخرى'
    };
    return typeMap[type] || type;
  };

  const formatStatus = (status: string) => {
    const statusMap: Record<string, string> = {
      'OPEN': 'مفتوح',
      'IN_PROGRESS': 'قيد المعالجة',
      'UNDER_INVESTIGATION': 'قيد التحقيق',
      'CLOSED': 'مغلق'
    };
    return statusMap[status] || status;
  };

  const formatSeverity = (severity: string) => {
    const severityMap: Record<string, string> = {
      'CRITICAL': 'حرج',
      'HIGH': 'عالي',
      'MEDIUM': 'متوسط',
      'LOW': 'منخفض'
    };
    return severityMap[severity] || severity;
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="card">
                <div className="card-body">
                  <div className="h-16 bg-dark-700 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="p-6">
        <div className="text-center text-gray-400">
          فشل في تحميل بيانات لوحة التحكم
        </div>
      </div>
    );
  }

  // Prepare chart data
  const severityData = Object.entries(stats.incidents_by_severity).map(([key, value]) => ({
    name: formatSeverity(key),
    value,
    color: getSeverityColor(key)
  }));

  const statusData = Object.entries(stats.incidents_by_status).map(([key, value]) => ({
    name: formatStatus(key),
    value,
    color: getStatusColor(key)
  }));

  const typeData = Object.entries(stats.incidents_by_type)
    .filter(([_, value]) => value > 0)
    .map(([key, value]) => ({
      name: formatIncidentType(key),
      value
    }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 6);

  // Get current date and time in Arabic
  const getCurrentDateTime = () => {
    const now = new Date();
    const arabicDate = now.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    });
    const arabicTime = now.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
    return `${arabicDate} - ${arabicTime}`;
  };

  return (
    <div className="min-h-screen bg-dark-950 p-4 md:p-6">
      <div className="max-w-7xl mx-auto space-y-6 md:space-y-8">
        {/* Header with Logo and Last Update */}
        <div className="bg-dark-800 border border-dark-700 rounded-xl p-4 md:p-6 shadow-lg">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-primary-600 p-3 rounded-xl shadow-lg">
                <ShieldCheckIcon className="h-6 w-6 md:h-8 md:w-8 text-white" />
              </div>
              <div>
                <h1 className="text-xl md:text-3xl font-bold text-gray-100 mb-1">
                  نظام إدارة الحوادث الأمنية
                </h1>
                <p className="text-gray-400 text-sm md:text-lg">
                  لوحة التحكم الرئيسية - نظرة شاملة على الحوادث الأمنية
                </p>
              </div>
            </div>
            <div className="text-left hidden md:block">
              <div className="flex items-center text-sm text-gray-400 mb-1">
                <ClockIcon className="h-4 w-4 ml-2" />
                آخر تحديث
              </div>
              <p className="text-gray-300 font-medium">
                {getCurrentDateTime()}
              </p>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <StatsCard
            title="إجمالي الحوادث"
            value={stats.total_incidents}
            icon={ChartBarIcon}
            color="blue"
          />
          <StatsCard
            title="الحوادث المفتوحة"
            value={stats.open_incidents}
            icon={ExclamationCircleIcon}
            color="red"
          />
          <StatsCard
            title="الحوادث الحرجة"
            value={stats.critical_incidents}
            icon={FireIcon}
            color="purple"
          />
          <StatsCard
            title="الحوادث المغلقة"
            value={stats.closed_incidents}
            icon={CheckCircleIcon}
            color="green"
          />
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8">
          {/* Severity Distribution */}
          <div className="bg-dark-800 border border-dark-700 rounded-xl shadow-lg">
            <div className="p-6 border-b border-dark-700">
              <h3 className="text-xl font-bold text-gray-100 mb-2">توزيع الحوادث حسب الخطورة</h3>
              <p className="text-gray-400 text-sm">تصنيف الحوادث حسب مستوى الخطورة</p>
            </div>
            <div className="p-6">
              <ResponsiveContainer width="100%" height={350}>
                <PieChart>
                  <Pie
                    data={severityData}
                    cx="50%"
                    cy="45%"
                    outerRadius={100}
                    innerRadius={40}
                    dataKey="value"
                    stroke="#1e293b"
                    strokeWidth={2}
                  >
                    {severityData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#1e293b',
                      border: '1px solid #334155',
                      borderRadius: '8px',
                      color: '#f1f5f9'
                    }}
                    formatter={(value, name) => [`${value} حادث`, name]}
                  />
                  <Legend
                    verticalAlign="bottom"
                    height={36}
                    wrapperStyle={{ color: '#94a3b8', fontSize: '14px' }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Status Distribution */}
          <div className="bg-dark-800 border border-dark-700 rounded-xl shadow-lg">
            <div className="p-6 border-b border-dark-700">
              <h3 className="text-xl font-bold text-gray-100 mb-2">توزيع الحوادث حسب الحالة</h3>
              <p className="text-gray-400 text-sm">حالة معالجة الحوادث الأمنية</p>
            </div>
            <div className="p-6">
              <ResponsiveContainer width="100%" height={350}>
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="45%"
                    outerRadius={100}
                    innerRadius={40}
                    dataKey="value"
                    stroke="#1e293b"
                    strokeWidth={2}
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#1e293b',
                      border: '1px solid #334155',
                      borderRadius: '8px',
                      color: '#f1f5f9'
                    }}
                    formatter={(value, name) => [`${value} حادث`, name]}
                  />
                  <Legend
                    verticalAlign="bottom"
                    height={36}
                    wrapperStyle={{ color: '#94a3b8', fontSize: '14px' }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* Incident Types Chart */}
        <div className="bg-dark-800 border border-dark-700 rounded-xl shadow-lg">
          <div className="p-6 border-b border-dark-700">
            <h3 className="text-xl font-bold text-gray-100 mb-2">أنواع الحوادث الأكثر شيوعاً</h3>
            <p className="text-gray-400 text-sm">إحصائيات تفصيلية لأنواع الحوادث الأمنية</p>
          </div>
          <div className="p-6">
            <ResponsiveContainer width="100%" height={400}>
              <BarChart
                data={typeData}
                margin={{ top: 20, right: 30, left: 20, bottom: 100 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#334155" opacity={0.3} />
                <XAxis
                  dataKey="name"
                  stroke="#94a3b8"
                  fontSize={12}
                  angle={-45}
                  textAnchor="end"
                  height={100}
                  interval={0}
                  tick={{ fill: '#94a3b8' }}
                />
                <YAxis
                  stroke="#94a3b8"
                  fontSize={12}
                  tick={{ fill: '#94a3b8' }}
                  label={{
                    value: 'عدد الحوادث',
                    angle: -90,
                    position: 'insideLeft',
                    style: { textAnchor: 'middle', fill: '#94a3b8' }
                  }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1e293b',
                    border: '1px solid #334155',
                    borderRadius: '8px',
                    color: '#f1f5f9'
                  }}
                  formatter={(value, name) => [`${value} حادث`, 'العدد']}
                  labelFormatter={(label) => `نوع الحادث: ${label}`}
                />
                <Bar
                  dataKey="value"
                  radius={[6, 6, 0, 0]}
                  stroke="#1e40af"
                  strokeWidth={1}
                >
                  {typeData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={`hsl(${220 + index * 30}, 70%, ${60 + index * 5}%)`}
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Recent Incidents Table */}
        <div className="bg-dark-800 border border-dark-700 rounded-2xl shadow-lg">
          <div className="p-6 border-b border-dark-700">
            <h3 className="text-xl font-bold text-gray-100 mb-2">الحوادث الأخيرة</h3>
            <p className="text-gray-400 text-sm">آخر الحوادث المسجلة في النظام مع تفاصيل شاملة</p>
          </div>
          <div className="p-0">
            <RecentIncidentsTable incidents={recentIncidents} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
