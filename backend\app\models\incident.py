from sqlalchemy import Column, Integer, String, Text, DateTime, Enum, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from ..core.database import Base

class IncidentType(enum.Enum):
    MALWARE_INFECTION = "Malware Infection"
    PHISHING_ATTACK = "Phishing Attack"
    DATA_BREACH = "Data Breach"
    UNAUTHORIZED_ACCESS = "Unauthorized Access"
    DDOS_ATTACK = "DDoS Attack"
    INSIDER_THREAT = "Insider Threat"
    SYSTEM_COMPROMISE = "System Compromise"
    NETWORK_INTRUSION = "Network Intrusion"
    SOCIAL_ENGINEERING = "Social Engineering"
    PHYSICAL_SECURITY_BREACH = "Physical Security Breach"
    OTHER = "Other"

class SeverityLevel(enum.Enum):
    CRITICAL = "Critical"
    HIGH = "High"
    MEDIUM = "Medium"
    LOW = "Low"

class IncidentStatus(enum.Enum):
    OPEN = "Open"
    IN_PROGRESS = "In Progress"
    UNDER_INVESTIGATION = "Under Investigation"
    CLOSED = "Closed"

class Incident(Base):
    __tablename__ = "incidents"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    incident_title = Column(String(200), nullable=False)
    incident_type = Column(Enum(IncidentType), nullable=False)
    incident_date = Column(DateTime(timezone=True), nullable=False)
    affected_system = Column(String(200), nullable=False)
    impact_description = Column(Text, nullable=False)
    severity_level = Column(Enum(SeverityLevel), nullable=False)
    actions_taken = Column(Text, nullable=True)
    status = Column(Enum(IncidentStatus), nullable=False, default=IncidentStatus.OPEN)
    attachments = Column(Text, nullable=True)  # JSON string of file paths
    
    # Foreign keys
    reporter_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    assigned_to = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    reporter = relationship("User", foreign_keys=[reporter_id], back_populates="reported_incidents")
    assignee = relationship("User", foreign_keys=[assigned_to], back_populates="assigned_incidents")
    
    def __repr__(self):
        return f"<Incident(id={self.id}, title='{self.incident_title}', status='{self.status.value}')>"
    
    @property
    def is_open(self) -> bool:
        return self.status == IncidentStatus.OPEN
    
    @property
    def is_closed(self) -> bool:
        return self.status == IncidentStatus.CLOSED
    
    @property
    def is_critical(self) -> bool:
        return self.severity_level == SeverityLevel.CRITICAL
    
    @property
    def is_high_priority(self) -> bool:
        return self.severity_level in [SeverityLevel.CRITICAL, SeverityLevel.HIGH]
