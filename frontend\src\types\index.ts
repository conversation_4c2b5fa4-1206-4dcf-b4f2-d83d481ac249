// User types
export interface User {
  id: number;
  username: string;
  email?: string;
  role: 'admin' | 'analyst' | 'viewer';
  created_at: string;
  last_login?: string;
  is_active: boolean;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

// Incident types
export interface Incident {
  id: number;
  incident_title: string;
  incident_type: IncidentType;
  incident_date: string;
  affected_system: string;
  impact_description: string;
  severity_level: SeverityLevel;
  actions_taken?: string;
  status: IncidentStatus;
  attachments?: string;
  reporter_id: number;
  assigned_to?: number;
  created_at: string;
  updated_at: string;
  reporter?: {
    id: number;
    username: string;
    email?: string;
  };
  assignee?: {
    id: number;
    username: string;
    email?: string;
  };
}

export type IncidentType = 
  | 'MALWARE_INFECTION'
  | 'PHISHING_ATTACK'
  | 'DATA_BREACH'
  | 'UNAUTHORIZED_ACCESS'
  | 'DDOS_ATTACK'
  | 'INSIDER_THREAT'
  | 'SYSTEM_COMPROMISE'
  | 'NETWORK_INTRUSION'
  | 'SOCIAL_ENGINEERING'
  | 'PHYSICAL_SECURITY_BREACH'
  | 'OTHER';

export type SeverityLevel = 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';

export type IncidentStatus = 'OPEN' | 'IN_PROGRESS' | 'UNDER_INVESTIGATION' | 'CLOSED';

export interface IncidentCreate {
  incident_title: string;
  incident_type: IncidentType;
  incident_date: string;
  affected_system: string;
  impact_description: string;
  severity_level: SeverityLevel;
  actions_taken?: string;
  status?: IncidentStatus;
  assigned_to?: number;
}

export interface IncidentUpdate {
  incident_title?: string;
  incident_type?: IncidentType;
  incident_date?: string;
  affected_system?: string;
  impact_description?: string;
  severity_level?: SeverityLevel;
  actions_taken?: string;
  status?: IncidentStatus;
  assigned_to?: number;
}

export interface IncidentFilter {
  incident_type?: IncidentType;
  severity_level?: SeverityLevel;
  status?: IncidentStatus;
  reporter_id?: number;
  assigned_to?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface IncidentStats {
  total_incidents: number;
  open_incidents: number;
  closed_incidents: number;
  critical_incidents: number;
  high_priority_incidents: number;
  incidents_by_type: Record<string, number>;
  incidents_by_status: Record<string, number>;
  incidents_by_severity: Record<string, number>;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface ApiError {
  detail: string;
  status_code: number;
}

// Form types
export interface UserCreateForm {
  username: string;
  password: string;
  email?: string;
  role: 'admin' | 'analyst' | 'viewer';
  is_active: boolean;
}

export interface UserUpdateForm {
  username?: string;
  email?: string;
  role?: 'admin' | 'analyst' | 'viewer';
  is_active?: boolean;
  password?: string;
}

export interface ChangePasswordForm {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

// Configuration types
export interface SelectOption {
  value: string;
  label: string;
}

export interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  render?: (value: any, row: any) => React.ReactNode;
}

// Navigation types
export interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  current?: boolean;
  requiredRoles?: string[];
}
