import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import apiClient from '@/utils/api';
import { Incident, IncidentFilter } from '@/types';
import toast from 'react-hot-toast';

const IncidentList: React.FC = () => {
  const [incidents, setIncidents] = useState<Incident[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<IncidentFilter>({});
  const [showFilters, setShowFilters] = useState(false);
  const { user, hasRole } = useAuth();
  const router = useRouter();

  useEffect(() => {
    fetchIncidents();
  }, [filters, searchTerm]);

  const fetchIncidents = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (searchTerm) params.append('search', searchTerm);
      if (filters.incident_type) params.append('incident_type', filters.incident_type);
      if (filters.severity_level) params.append('severity_level', filters.severity_level);
      if (filters.status) params.append('status', filters.status);
      if (filters.assigned_to) params.append('assigned_to', filters.assigned_to.toString());
      if (filters.date_from) params.append('date_from', filters.date_from);
      if (filters.date_to) params.append('date_to', filters.date_to);

      const data = await apiClient.get<Incident[]>(`/incidents/?${params.toString()}`);
      setIncidents(data);
    } catch (error: any) {
      toast.error('فشل في تحميل الحوادث');
      console.error('Fetch incidents error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteIncident = async (incidentId: number) => {
    if (!hasRole('admin')) {
      toast.error('ليس لديك صلاحية لحذف الحوادث');
      return;
    }

    if (confirm('هل أنت متأكد من حذف هذا الحادث؟')) {
      try {
        await apiClient.delete(`/incidents/${incidentId}/`);
        toast.success('تم حذف الحادث بنجاح');
        fetchIncidents();
      } catch (error: any) {
        toast.error('فشل في حذف الحادث');
      }
    }
  };

  const formatIncidentType = (type: string) => {
    const typeMap: Record<string, string> = {
      'MALWARE_INFECTION': 'إصابة بالبرمجيات الخبيثة',
      'PHISHING_ATTACK': 'هجوم تصيد',
      'DATA_BREACH': 'تسريب بيانات',
      'UNAUTHORIZED_ACCESS': 'وصول غير مصرح',
      'DDOS_ATTACK': 'هجوم حجب الخدمة',
      'INSIDER_THREAT': 'تهديد داخلي',
      'SYSTEM_COMPROMISE': 'اختراق النظام',
      'NETWORK_INTRUSION': 'تسلل الشبكة',
      'SOCIAL_ENGINEERING': 'هندسة اجتماعية',
      'PHYSICAL_SECURITY_BREACH': 'خرق الأمان المادي',
      'OTHER': 'أخرى'
    };
    return typeMap[type] || type;
  };

  const formatStatus = (status: string) => {
    const statusMap: Record<string, string> = {
      'OPEN': 'مفتوح',
      'IN_PROGRESS': 'قيد المعالجة',
      'UNDER_INVESTIGATION': 'قيد التحقيق',
      'CLOSED': 'مغلق'
    };
    return statusMap[status] || status;
  };

  const formatSeverity = (severity: string) => {
    const severityMap: Record<string, string> = {
      'CRITICAL': 'حرج',
      'HIGH': 'عالي',
      'MEDIUM': 'متوسط',
      'LOW': 'منخفض'
    };
    return severityMap[severity] || severity;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-100">الحوادث الأمنية</h1>
          <p className="text-gray-400">إدارة ومتابعة الحوادث الأمنية</p>
        </div>
        {hasRole(['admin', 'analyst']) && (
          <button
            onClick={() => router.push('/incidents/new')}
            className="btn-primary flex items-center mt-4 sm:mt-0"
          >
            <PlusIcon className="h-5 w-5 ml-2" />
            إضافة حادث جديد
          </button>
        )}
      </div>

      {/* Search and Filters */}
      <div className="card">
        <div className="card-body">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="البحث في الحوادث..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input-field w-full pr-10"
                />
              </div>
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="btn-secondary flex items-center"
            >
              <FunnelIcon className="h-5 w-5 ml-2" />
              الفلاتر
            </button>
          </div>

          {showFilters && (
            <div className="mt-4 pt-4 border-t border-dark-600">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    نوع الحادث
                  </label>
                  <select
                    value={filters.incident_type || ''}
                    onChange={(e) => setFilters({ ...filters, incident_type: e.target.value as any })}
                    className="input-field w-full"
                  >
                    <option value="">جميع الأنواع</option>
                    <option value="MALWARE_INFECTION">إصابة بالبرمجيات الخبيثة</option>
                    <option value="PHISHING_ATTACK">هجوم تصيد</option>
                    <option value="DATA_BREACH">تسريب بيانات</option>
                    <option value="UNAUTHORIZED_ACCESS">وصول غير مصرح</option>
                    <option value="DDOS_ATTACK">هجوم حجب الخدمة</option>
                    <option value="OTHER">أخرى</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    مستوى الخطورة
                  </label>
                  <select
                    value={filters.severity_level || ''}
                    onChange={(e) => setFilters({ ...filters, severity_level: e.target.value as any })}
                    className="input-field w-full"
                  >
                    <option value="">جميع المستويات</option>
                    <option value="CRITICAL">حرج</option>
                    <option value="HIGH">عالي</option>
                    <option value="MEDIUM">متوسط</option>
                    <option value="LOW">منخفض</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    الحالة
                  </label>
                  <select
                    value={filters.status || ''}
                    onChange={(e) => setFilters({ ...filters, status: e.target.value as any })}
                    className="input-field w-full"
                  >
                    <option value="">جميع الحالات</option>
                    <option value="OPEN">مفتوح</option>
                    <option value="IN_PROGRESS">قيد المعالجة</option>
                    <option value="UNDER_INVESTIGATION">قيد التحقيق</option>
                    <option value="CLOSED">مغلق</option>
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Incidents Table */}
      <div className="card">
        <div className="card-body p-0">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="spinner w-8 h-8"></div>
            </div>
          ) : incidents.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-dark-600">
                <thead className="table-header">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">
                      العنوان
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">
                      النوع
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">
                      الخطورة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">
                      الحالة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">
                      المبلغ
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">
                      التاريخ
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-dark-600">
                  {incidents.map((incident) => (
                    <tr key={incident.id} className="table-row">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-100">
                          {incident.incident_title}
                        </div>
                        <div className="text-sm text-gray-400">
                          {incident.affected_system}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {formatIncidentType(incident.incident_type)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`badge badge-${incident.severity_level.toLowerCase()}`}>
                          {formatSeverity(incident.severity_level)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`badge badge-${incident.status.toLowerCase().replace('_', '-')}`}>
                          {formatStatus(incident.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {incident.reporter?.username}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {formatDate(incident.created_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            onClick={() => router.push(`/incidents/${incident.id}`)}
                            className="text-blue-400 hover:text-blue-300"
                            title="عرض"
                          >
                            <EyeIcon className="h-5 w-5" />
                          </button>
                          {hasRole(['admin', 'analyst']) && (
                            <button
                              onClick={() => router.push(`/incidents/${incident.id}/edit`)}
                              className="text-yellow-400 hover:text-yellow-300"
                              title="تعديل"
                            >
                              <PencilIcon className="h-5 w-5" />
                            </button>
                          )}
                          {hasRole('admin') && (
                            <button
                              onClick={() => handleDeleteIncident(incident.id)}
                              className="text-red-400 hover:text-red-300"
                              title="حذف"
                            >
                              <TrashIcon className="h-5 w-5" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-300">لا توجد حوادث</h3>
              <p className="mt-1 text-sm text-gray-400">
                {hasRole(['admin', 'analyst']) ? 'ابدأ بإضافة حادث جديد' : 'لم يتم الإبلاغ عن أي حوادث بعد'}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default IncidentList;
