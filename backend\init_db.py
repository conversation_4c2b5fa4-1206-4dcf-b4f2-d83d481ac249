#!/usr/bin/env python3
"""
Database initialization script for the Cybersecurity Incident Reporting System.
This script creates the database tables and inserts default data including a default admin user.
"""

import sys
import os
from datetime import datetime

# Add the app directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import engine, Base, SessionLocal
from app.core.security import get_password_hash
from app.models.user import User, UserRole
from app.models.incident import Incident, IncidentType, SeverityLevel, IncidentStatus

def create_tables():
    """Create all database tables."""
    print("Creating database tables...")
    Base.metadata.create_all(bind=engine)
    print("✓ Database tables created successfully")

def create_default_admin():
    """Create default admin user."""
    db = SessionLocal()
    try:
        # Check if admin user already exists
        existing_admin = db.query(User).filter(User.username == "admin").first()
        if existing_admin:
            print("✓ Default admin user already exists")
            return
        
        # Create default admin user
        admin_user = User(
            username="admin",
            hashed_password=get_password_hash("admin123"),
            email="<EMAIL>",
            role=UserRole.ADMIN,
            is_active=True,
            created_at=datetime.utcnow()
        )
        
        db.add(admin_user)
        db.commit()
        print("✓ Default admin user created successfully")
        print("  Username: admin")
        print("  Password: admin123")
        print("  Role: Administrator")
        
    except Exception as e:
        print(f"✗ Error creating default admin user: {e}")
        db.rollback()
    finally:
        db.close()

def create_sample_users():
    """Create sample users for testing."""
    db = SessionLocal()
    try:
        # Check if sample users already exist
        existing_analyst = db.query(User).filter(User.username == "analyst").first()
        if existing_analyst:
            print("✓ Sample users already exist")
            return
        
        # Create sample analyst user
        analyst_user = User(
            username="analyst",
            hashed_password=get_password_hash("analyst123"),
            email="<EMAIL>",
            role=UserRole.ANALYST,
            is_active=True,
            created_at=datetime.utcnow()
        )
        
        # Create sample viewer user
        viewer_user = User(
            username="viewer",
            hashed_password=get_password_hash("viewer123"),
            email="<EMAIL>",
            role=UserRole.VIEWER,
            is_active=True,
            created_at=datetime.utcnow()
        )
        
        db.add(analyst_user)
        db.add(viewer_user)
        db.commit()
        
        print("✓ Sample users created successfully")
        print("  Analyst - Username: analyst, Password: analyst123")
        print("  Viewer - Username: viewer, Password: viewer123")
        
    except Exception as e:
        print(f"✗ Error creating sample users: {e}")
        db.rollback()
    finally:
        db.close()

def create_sample_incidents():
    """Create sample incidents for testing."""
    db = SessionLocal()
    try:
        # Check if sample incidents already exist
        existing_incident = db.query(Incident).first()
        if existing_incident:
            print("✓ Sample incidents already exist")
            return
        
        # Get admin user for sample incidents
        admin_user = db.query(User).filter(User.username == "admin").first()
        analyst_user = db.query(User).filter(User.username == "analyst").first()
        
        if not admin_user or not analyst_user:
            print("✗ Cannot create sample incidents: Required users not found")
            return
        
        # Create sample incidents
        sample_incidents = [
            {
                "incident_title": "Suspicious Email Attachment Detected",
                "incident_type": IncidentType.PHISHING_ATTACK,
                "incident_date": datetime.utcnow(),
                "affected_system": "Email Server - Exchange 2019",
                "impact_description": "Potential phishing email with malicious attachment sent to 15 employees. Email quarantined and users notified.",
                "severity_level": SeverityLevel.HIGH,
                "actions_taken": "Email quarantined, affected users notified, antivirus signatures updated",
                "status": IncidentStatus.UNDER_INVESTIGATION,
                "reporter_id": admin_user.id,
                "assigned_to": analyst_user.id
            },
            {
                "incident_title": "Unauthorized Access Attempt on Database Server",
                "incident_type": IncidentType.UNAUTHORIZED_ACCESS,
                "incident_date": datetime.utcnow(),
                "affected_system": "Database Server - SQL Server 2019",
                "impact_description": "Multiple failed login attempts detected on production database server from external IP address.",
                "severity_level": SeverityLevel.CRITICAL,
                "actions_taken": "IP address blocked, passwords reset, additional monitoring enabled",
                "status": IncidentStatus.IN_PROGRESS,
                "reporter_id": analyst_user.id,
                "assigned_to": admin_user.id
            },
            {
                "incident_title": "Workstation Malware Detection",
                "incident_type": IncidentType.MALWARE_INFECTION,
                "incident_date": datetime.utcnow(),
                "affected_system": "Employee Workstation - WS-001",
                "impact_description": "Malware detected on employee workstation during routine scan. System isolated from network.",
                "severity_level": SeverityLevel.MEDIUM,
                "actions_taken": "System isolated, malware removed, full system scan completed",
                "status": IncidentStatus.CLOSED,
                "reporter_id": analyst_user.id,
                "assigned_to": analyst_user.id
            }
        ]
        
        for incident_data in sample_incidents:
            incident = Incident(**incident_data)
            db.add(incident)
        
        db.commit()
        print("✓ Sample incidents created successfully")
        
    except Exception as e:
        print(f"✗ Error creating sample incidents: {e}")
        db.rollback()
    finally:
        db.close()

def main():
    """Main initialization function."""
    print("=" * 60)
    print("Cybersecurity Incident Reporting System - Database Setup")
    print("=" * 60)
    
    try:
        # Create tables
        create_tables()
        
        # Create default admin user
        create_default_admin()
        
        # Create sample users
        create_sample_users()
        
        # Create sample incidents
        create_sample_incidents()
        
        print("\n" + "=" * 60)
        print("Database initialization completed successfully!")
        print("=" * 60)
        print("\nDefault Login Credentials:")
        print("Admin: admin / admin123")
        print("Analyst: analyst / analyst123")
        print("Viewer: viewer / viewer123")
        print("\nYou can now start the FastAPI server with:")
        print("uvicorn main:app --reload")
        
    except Exception as e:
        print(f"\n✗ Database initialization failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
