from sqlalchemy import Column, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from ..core.database import Base

class UserRole(enum.Enum):
    ADMIN = "admin"
    ANALYST = "analyst"
    VIEWER = "viewer"

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    email = Column(String(100), nullable=True)
    role = Column(Enum(UserRole), nullable=False, default=UserRole.VIEWER)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_login = Column(DateTime(timezone=True), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    reported_incidents = relationship("Incident", foreign_keys="Incident.reporter_id", back_populates="reporter")
    assigned_incidents = relationship("Incident", foreign_keys="Incident.assigned_to", back_populates="assignee")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role.value}')>"
    
    @property
    def is_admin(self) -> bool:
        return self.role == UserRole.ADMIN
    
    @property
    def is_analyst(self) -> bool:
        return self.role == UserRole.ANALYST
    
    @property
    def is_viewer(self) -> bool:
        return self.role == UserRole.VIEWER
    
    def can_manage_users(self) -> bool:
        return self.role == UserRole.ADMIN
    
    def can_create_incidents(self) -> bool:
        return self.role in [UserRole.ADMIN, UserRole.ANALYST]
    
    def can_modify_incidents(self) -> bool:
        return self.role in [UserRole.ADMIN, UserRole.ANALYST]
    
    def can_view_incidents(self) -> bool:
        return True  # All authenticated users can view incidents
