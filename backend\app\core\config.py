from pydantic_settings import BaseSettings
from typing import List
import os
from pydantic import field_validator

class Settings(BaseSettings):
    # Application settings
    APP_NAME: str = "Cybersecurity Incident Reporting System"
    VERSION: str = "1.0.0"
    DEBUG: bool = True
    
    # Database settings
    DATABASE_URL: str = "sqlite:///./database.db"
    
    # Security settings
    SECRET_KEY: str = "your-super-secret-key-change-this-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Password settings
    PASSWORD_MIN_LENGTH: int = 8
    PASSWORD_REQUIRE_UPPERCASE: bool = True
    PASSWORD_REQUIRE_LOWERCASE: bool = True
    PASSWORD_REQUIRE_NUMBERS: bool = True
    PASSWORD_REQUIRE_SPECIAL: bool = True
    
    # Rate limiting
    RATE_LIMIT_LOGIN: str = "5/minute"
    RATE_LIMIT_API: str = "100/minute"
    
    # CORS settings
    ALLOWED_HOSTS: str = "http://localhost:3000,http://127.0.0.1:3000,http://localhost:8000,http://127.0.0.1:8000"

    @field_validator('ALLOWED_HOSTS')
    @classmethod
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(',')]
        return v
    
    # File upload settings
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_FILE_TYPES: List[str] = [
        "image/jpeg", "image/png", "image/gif",
        "application/pdf", "text/plain",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    ]
    UPLOAD_DIR: str = "uploads"
    
    # Incident settings
    INCIDENT_TYPES: List[str] = [
        "Malware Infection",
        "Phishing Attack",
        "Data Breach",
        "Unauthorized Access",
        "DDoS Attack",
        "Insider Threat",
        "System Compromise",
        "Network Intrusion",
        "Social Engineering",
        "Physical Security Breach",
        "Other"
    ]
    
    SEVERITY_LEVELS: List[str] = ["Critical", "High", "Medium", "Low"]
    INCIDENT_STATUSES: List[str] = ["Open", "In Progress", "Under Investigation", "Closed"]
    
    # User roles
    USER_ROLES: List[str] = ["admin", "analyst", "viewer"]
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Create settings instance
settings = Settings()

# Ensure upload directory exists
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
