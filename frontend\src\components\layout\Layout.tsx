import React, { useState, ReactNode } from 'react';
import { useRouter } from 'next/router';
import {
  HomeIcon,
  ExclamationTriangleIcon,
  UsersIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon,
  UserCircleIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import { NavItem } from '@/types';

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, logout, hasRole } = useAuth();
  const router = useRouter();

  const navigation: NavItem[] = [
    {
      name: 'لوحة التحكم',
      href: '/dashboard',
      icon: HomeIcon,
      current: router.pathname === '/dashboard',
    },
    {
      name: 'الحوادث الأمنية',
      href: '/incidents',
      icon: ExclamationTriangleIcon,
      current: router.pathname.startsWith('/incidents'),
    },
    {
      name: 'إدارة المستخدمين',
      href: '/users',
      icon: UsersIcon,
      current: router.pathname.startsWith('/users'),
      requiredRoles: ['admin'],
    },
    {
      name: 'التقارير',
      href: '/reports',
      icon: ChartBarIcon,
      current: router.pathname.startsWith('/reports'),
    },
    {
      name: 'الإعدادات',
      href: '/settings',
      icon: Cog6ToothIcon,
      current: router.pathname.startsWith('/settings'),
    },
  ];

  const filteredNavigation = navigation.filter(item => 
    !item.requiredRoles || hasRole(item.requiredRoles)
  );

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="min-h-screen bg-dark-950" dir="rtl">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 right-0 w-64 bg-dark-900 shadow-xl">
          <div className="flex items-center justify-between h-16 px-4 border-b border-dark-700">
            <h2 className="text-lg font-semibold text-white">نظام الحوادث الأمنية</h2>
            <button
              onClick={() => setSidebarOpen(false)}
              className="text-gray-400 hover:text-white"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          <nav className="mt-4 px-4 space-y-2">
            {filteredNavigation.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className={`sidebar-link ${item.current ? 'active' : ''}`}
                onClick={() => setSidebarOpen(false)}
              >
                <item.icon className="h-5 w-5 mr-3" />
                {item.name}
              </a>
            ))}
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:right-0 lg:w-64 lg:block">
        <div className="flex flex-col h-full bg-dark-900 border-l border-dark-700">
          <div className="flex items-center h-16 px-4 border-b border-dark-700">
            <h2 className="text-lg font-semibold text-white">نظام الحوادث الأمنية</h2>
          </div>
          <nav className="flex-1 mt-4 px-4 space-y-2">
            {filteredNavigation.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className={`sidebar-link ${item.current ? 'active' : ''}`}
              >
                <item.icon className="h-5 w-5 mr-3" />
                {item.name}
              </a>
            ))}
          </nav>
          
          {/* User info and logout */}
          <div className="border-t border-dark-700 p-4">
            <div className="flex items-center mb-3">
              <UserCircleIcon className="h-8 w-8 text-gray-400 ml-3" />
              <div>
                <p className="text-sm font-medium text-white">{user?.username}</p>
                <p className="text-xs text-gray-400">
                  {user?.role === 'admin' ? 'مدير' : 
                   user?.role === 'analyst' ? 'محلل أمني' : 'مشاهد'}
                </p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-3 py-2 text-sm text-gray-300 hover:bg-dark-700 hover:text-white rounded-lg transition-colors duration-200"
            >
              <ArrowRightOnRectangleIcon className="h-5 w-5 ml-3" />
              تسجيل الخروج
            </button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:mr-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 bg-dark-900 border-b border-dark-700 lg:hidden">
          <div className="flex items-center justify-between h-16 px-4">
            <h1 className="text-lg font-semibold text-white">نظام الحوادث الأمنية</h1>
            <button
              onClick={() => setSidebarOpen(true)}
              className="text-gray-400 hover:text-white"
            >
              <Bars3Icon className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1">
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;
