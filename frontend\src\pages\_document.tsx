import { Html, Head, Main, NextScript } from 'next/document';

export default function Document() {
  return (
    <Html lang="ar" dir="rtl">
      <Head>
        <meta charSet="utf-8" />
        <meta name="description" content="نظام الإبلاغ عن الحوادث الأمنية - نظام شامل لإدارة ومتابعة الحوادث الأمنية" />
        <meta name="keywords" content="أمن المعلومات, الحوادث الأمنية, إدارة الحوادث, الأمن السيبراني" />
        <meta name="author" content="نظام الإبلاغ عن الحوادث الأمنية" />

        <link rel="icon" href="/favicon.ico" />
        
        {/* Preload Arabic fonts */}
        <link
          rel="preload"
          href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap"
          as="style"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap"
          rel="stylesheet"
        />
        
        {/* Security headers */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        <meta name="referrer" content="strict-origin-when-cross-origin" />
      </Head>
      <body className="bg-dark-950 text-gray-100 font-arabic">
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
