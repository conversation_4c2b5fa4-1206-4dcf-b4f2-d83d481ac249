"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "./src/components/dashboard/Dashboard.tsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/Dashboard.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckCircleIcon_ClockIcon_ExclamationCircleIcon_FireIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckCircleIcon,ClockIcon,ExclamationCircleIcon,FireIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ChartBarIcon,CheckCircleIcon,ClockIcon,ExclamationCircleIcon,FireIcon,ShieldCheckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!./node_modules/recharts/es6/index.js\");\n/* harmony import */ var _StatsCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./StatsCard */ \"./src/components/dashboard/StatsCard.tsx\");\n/* harmony import */ var _RecentIncidentsTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./RecentIncidentsTable */ \"./src/components/dashboard/RecentIncidentsTable.tsx\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/api */ \"./src/utils/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst Dashboard = ()=>{\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentIncidents, setRecentIncidents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchDashboardData();\n    }, []);\n    const fetchDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            const [statsData, incidentsData] = await Promise.all([\n                _utils_api__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/incidents/stats/dashboard/\"),\n                _utils_api__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/incidents/?limit=5\")\n            ]);\n            setStats(statsData);\n            setRecentIncidents(incidentsData);\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"فشل في تحميل بيانات لوحة التحكم\");\n            console.error(\"Dashboard data fetch error:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getSeverityColor = (severity)=>{\n        switch(severity.toLowerCase()){\n            case \"critical\":\n                return \"#ef4444\"; // أحمر فاتح للحرج\n            case \"high\":\n                return \"#f97316\"; // برتقالي فاتح للعالي\n            case \"medium\":\n                return \"#eab308\"; // أصفر فاتح للمتوسط\n            case \"low\":\n                return \"#22c55e\"; // أخضر فاتح للمنخفض\n            default:\n                return \"#94a3b8\"; // رمادي فاتح\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case \"open\":\n                return \"#ef4444\"; // أحمر فاتح للمفتوح\n            case \"in_progress\":\n                return \"#f59e0b\"; // برتقالي فاتح لقيد المعالجة\n            case \"under_investigation\":\n                return \"#3b82f6\"; // أزرق فاتح لقيد التحقيق\n            case \"closed\":\n                return \"#10b981\"; // أخضر فاتح للمغلق\n            default:\n                return \"#94a3b8\"; // رمادي فاتح\n        }\n    };\n    const formatIncidentType = (type)=>{\n        const typeMap = {\n            \"MALWARE_INFECTION\": \"إصابة بالبرمجيات الخبيثة\",\n            \"PHISHING_ATTACK\": \"هجوم تصيد\",\n            \"DATA_BREACH\": \"تسريب بيانات\",\n            \"UNAUTHORIZED_ACCESS\": \"وصول غير مصرح\",\n            \"DDOS_ATTACK\": \"هجوم حجب الخدمة\",\n            \"INSIDER_THREAT\": \"تهديد داخلي\",\n            \"SYSTEM_COMPROMISE\": \"اختراق النظام\",\n            \"NETWORK_INTRUSION\": \"تسلل الشبكة\",\n            \"SOCIAL_ENGINEERING\": \"هندسة اجتماعية\",\n            \"PHYSICAL_SECURITY_BREACH\": \"خرق الأمان المادي\",\n            \"OTHER\": \"أخرى\"\n        };\n        return typeMap[type] || type;\n    };\n    const formatStatus = (status)=>{\n        const statusMap = {\n            \"OPEN\": \"مفتوح\",\n            \"IN_PROGRESS\": \"قيد المعالجة\",\n            \"UNDER_INVESTIGATION\": \"قيد التحقيق\",\n            \"CLOSED\": \"مغلق\"\n        };\n        return statusMap[status] || status;\n    };\n    const formatSeverity = (severity)=>{\n        const severityMap = {\n            \"CRITICAL\": \"حرج\",\n            \"HIGH\": \"عالي\",\n            \"MEDIUM\": \"متوسط\",\n            \"LOW\": \"منخفض\"\n        };\n        return severityMap[severity] || severity;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-body\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-16 bg-dark-700 rounded\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 17\n                            }, undefined)\n                        }, i, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                lineNumber: 106,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!stats) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-gray-400\",\n                children: \"فشل في تحميل بيانات لوحة التحكم\"\n            }, void 0, false, {\n                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                lineNumber: 124,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Prepare chart data\n    const severityData = Object.entries(stats.incidents_by_severity).map((param)=>{\n        let [key, value] = param;\n        return {\n            name: formatSeverity(key),\n            value,\n            color: getSeverityColor(key)\n        };\n    });\n    const statusData = Object.entries(stats.incidents_by_status).map((param)=>{\n        let [key, value] = param;\n        return {\n            name: formatStatus(key),\n            value,\n            color: getStatusColor(key)\n        };\n    });\n    const typeData = Object.entries(stats.incidents_by_type).filter((param)=>{\n        let [_, value] = param;\n        return value > 0;\n    }).map((param)=>{\n        let [key, value] = param;\n        return {\n            name: formatIncidentType(key),\n            value\n        };\n    }).sort((a, b)=>b.value - a.value).slice(0, 6);\n    // Get current date and time in Arabic\n    const getCurrentDateTime = ()=>{\n        const now = new Date();\n        const arabicDate = now.toLocaleDateString(\"ar-SA\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            weekday: \"long\"\n        });\n        const arabicTime = now.toLocaleTimeString(\"ar-SA\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: false\n        });\n        return \"\".concat(arabicDate, \" - \").concat(arabicTime);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-dark-950 p-4 md:p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-6 md:space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-dark-800 border border-dark-700 rounded-xl p-4 md:p-6 shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-primary-600 p-3 rounded-xl shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckCircleIcon_ClockIcon_ExclamationCircleIcon_FireIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ShieldCheckIcon, {\n                                            className: \"h-6 w-6 md:h-8 md:w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl md:text-3xl font-bold text-gray-100 mb-1\",\n                                                children: \"نظام إدارة الحوادث الأمنية\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm md:text-lg\",\n                                                children: \"لوحة التحكم الرئيسية - نظرة شاملة على الحوادث الأمنية\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-left hidden md:block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-sm text-gray-400 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckCircleIcon_ClockIcon_ExclamationCircleIcon_FireIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ClockIcon, {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"آخر تحديث\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 font-medium\",\n                                        children: getCurrentDateTime()\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            title: \"إجمالي الحوادث\",\n                            value: stats.total_incidents,\n                            icon: _barrel_optimize_names_ChartBarIcon_CheckCircleIcon_ClockIcon_ExclamationCircleIcon_FireIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ChartBarIcon,\n                            color: \"blue\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            title: \"الحوادث المفتوحة\",\n                            value: stats.open_incidents,\n                            icon: _barrel_optimize_names_ChartBarIcon_CheckCircleIcon_ClockIcon_ExclamationCircleIcon_FireIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ExclamationCircleIcon,\n                            color: \"red\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            title: \"الحوادث الحرجة\",\n                            value: stats.critical_incidents,\n                            icon: _barrel_optimize_names_ChartBarIcon_CheckCircleIcon_ClockIcon_ExclamationCircleIcon_FireIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.FireIcon,\n                            color: \"purple\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            title: \"الحوادث المغلقة\",\n                            value: stats.closed_incidents,\n                            icon: _barrel_optimize_names_ChartBarIcon_CheckCircleIcon_ClockIcon_ExclamationCircleIcon_FireIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.CheckCircleIcon,\n                            color: \"green\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-dark-800 border border-dark-700 rounded-xl shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-dark-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-100 mb-2\",\n                                            children: \"توزيع الحوادث حسب الخطورة\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"تصنيف الحوادث حسب مستوى الخطورة\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 350,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Pie, {\n                                                    data: severityData,\n                                                    cx: \"50%\",\n                                                    cy: \"45%\",\n                                                    outerRadius: 100,\n                                                    innerRadius: 40,\n                                                    dataKey: \"value\",\n                                                    stroke: \"#1e293b\",\n                                                    strokeWidth: 2,\n                                                    children: severityData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Cell, {\n                                                            fill: entry.color\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                                    contentStyle: {\n                                                        backgroundColor: \"#1e293b\",\n                                                        border: \"1px solid #334155\",\n                                                        borderRadius: \"8px\",\n                                                        color: \"#f1f5f9\"\n                                                    },\n                                                    formatter: (value, name)=>[\n                                                            \"\".concat(value, \" حادث\"),\n                                                            name\n                                                        ]\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Legend, {\n                                                    verticalAlign: \"bottom\",\n                                                    height: 36,\n                                                    wrapperStyle: {\n                                                        color: \"#94a3b8\",\n                                                        fontSize: \"14px\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-dark-800 border border-dark-700 rounded-xl shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-dark-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-100 mb-2\",\n                                            children: \"توزيع الحوادث حسب الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"حالة معالجة الحوادث الأمنية\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 350,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Pie, {\n                                                    data: statusData,\n                                                    cx: \"50%\",\n                                                    cy: \"45%\",\n                                                    outerRadius: 100,\n                                                    innerRadius: 40,\n                                                    dataKey: \"value\",\n                                                    stroke: \"#1e293b\",\n                                                    strokeWidth: 2,\n                                                    children: statusData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Cell, {\n                                                            fill: entry.color\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                                    contentStyle: {\n                                                        backgroundColor: \"#1e293b\",\n                                                        border: \"1px solid #334155\",\n                                                        borderRadius: \"8px\",\n                                                        color: \"#f1f5f9\"\n                                                    },\n                                                    formatter: (value, name)=>[\n                                                            \"\".concat(value, \" حادث\"),\n                                                            name\n                                                        ]\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Legend, {\n                                                    verticalAlign: \"bottom\",\n                                                    height: 36,\n                                                    wrapperStyle: {\n                                                        color: \"#94a3b8\",\n                                                        fontSize: \"14px\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-dark-800 border border-dark-700 rounded-xl shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-dark-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-100 mb-2\",\n                                    children: \"أنواع الحوادث الأكثر شيوعاً\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: \"إحصائيات تفصيلية لأنواع الحوادث الأمنية\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.ResponsiveContainer, {\n                                width: \"100%\",\n                                height: 400,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.BarChart, {\n                                    data: typeData,\n                                    margin: {\n                                        top: 20,\n                                        right: 30,\n                                        left: 20,\n                                        bottom: 100\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.CartesianGrid, {\n                                            strokeDasharray: \"3 3\",\n                                            stroke: \"#334155\",\n                                            opacity: 0.3\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.XAxis, {\n                                            dataKey: \"name\",\n                                            stroke: \"#94a3b8\",\n                                            fontSize: 12,\n                                            angle: -45,\n                                            textAnchor: \"end\",\n                                            height: 100,\n                                            interval: 0,\n                                            tick: {\n                                                fill: \"#94a3b8\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.YAxis, {\n                                            stroke: \"#94a3b8\",\n                                            fontSize: 12,\n                                            tick: {\n                                                fill: \"#94a3b8\"\n                                            },\n                                            label: {\n                                                value: \"عدد الحوادث\",\n                                                angle: -90,\n                                                position: \"insideLeft\",\n                                                style: {\n                                                    textAnchor: \"middle\",\n                                                    fill: \"#94a3b8\"\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                            contentStyle: {\n                                                backgroundColor: \"#1e293b\",\n                                                border: \"1px solid #334155\",\n                                                borderRadius: \"8px\",\n                                                color: \"#f1f5f9\"\n                                            },\n                                            formatter: (value, name)=>[\n                                                    \"\".concat(value, \" حادث\"),\n                                                    \"العدد\"\n                                                ],\n                                            labelFormatter: (label)=>\"نوع الحادث: \".concat(label)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Bar, {\n                                            dataKey: \"value\",\n                                            radius: [\n                                                6,\n                                                6,\n                                                0,\n                                                0\n                                            ],\n                                            stroke: \"#1e40af\",\n                                            strokeWidth: 1,\n                                            children: typeData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Cell, {\n                                                    fill: \"hsl(\".concat(220 + index * 30, \", 70%, \").concat(60 + index * 5, \"%)\")\n                                                }, \"cell-\".concat(index), false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-dark-800 border border-dark-700 rounded-2xl shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-dark-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-100 mb-2\",\n                                    children: \"الحوادث الأخيرة\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: \"آخر الحوادث المسجلة في النظام مع تفاصيل شاملة\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RecentIncidentsTable__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                incidents: recentIncidents\n                            }, void 0, false, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Dashboard, \"Q5LdgZ+R63+eF+7ajBOguc5/oDA=\");\n_c = Dashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/dashboard/Dashboard.tsx\n"));

/***/ })

});