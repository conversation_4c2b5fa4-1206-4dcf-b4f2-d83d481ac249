import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import Layout from '@/components/layout/Layout';
import {
  DocumentArrowDownIcon,
  FunnelIcon,
  CalendarIcon,
  ChartBarIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  PieChart, 
  Pie, 
  Cell, 
  Legend,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import apiClient from '@/utils/api';
import { IncidentStats } from '@/types';
import toast from 'react-hot-toast';

const Reports: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<IncidentStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: ''
  });
  const [selectedFilter, setSelectedFilter] = useState('all');

  useEffect(() => {
    fetchReportsData();
  }, []);

  const fetchReportsData = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get('/api/incidents/stats/dashboard/');
      setStats(response.data);
    } catch (error: any) {
      toast.error('فشل في تحميل بيانات التقارير');
      console.error('Reports data fetch error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical': return '#dc2626'; // أحمر داكن
      case 'high': return '#ea580c';     // برتقالي
      case 'medium': return '#d97706';   // أصفر
      case 'low': return '#16a34a';      // أخضر
      default: return '#6b7280';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'open': return '#dc2626';
      case 'in_progress': return '#d97706';
      case 'under_investigation': return '#2563eb';
      case 'closed': return '#16a34a';
      default: return '#6b7280';
    }
  };

  const formatSeverity = (severity: string) => {
    const severityMap: Record<string, string> = {
      'CRITICAL': 'حرج',
      'HIGH': 'عالي',
      'MEDIUM': 'متوسط',
      'LOW': 'منخفض'
    };
    return severityMap[severity] || severity;
  };

  const formatStatus = (status: string) => {
    const statusMap: Record<string, string> = {
      'OPEN': 'مفتوح',
      'IN_PROGRESS': 'قيد المعالجة',
      'UNDER_INVESTIGATION': 'قيد التحقيق',
      'CLOSED': 'مغلق'
    };
    return statusMap[status] || status;
  };

  const formatIncidentType = (type: string) => {
    const typeMap: Record<string, string> = {
      'MALWARE_INFECTION': 'إصابة بالبرمجيات الخبيثة',
      'PHISHING_ATTACK': 'هجوم تصيد',
      'DATA_BREACH': 'تسريب بيانات',
      'UNAUTHORIZED_ACCESS': 'وصول غير مصرح',
      'DDOS_ATTACK': 'هجوم حجب الخدمة',
      'INSIDER_THREAT': 'تهديد داخلي',
      'SYSTEM_COMPROMISE': 'اختراق النظام',
      'NETWORK_INTRUSION': 'تسلل الشبكة',
      'SOCIAL_ENGINEERING': 'هندسة اجتماعية',
      'PHYSICAL_SECURITY_BREACH': 'خرق الأمان المادي',
      'OTHER': 'أخرى'
    };
    return typeMap[type] || type;
  };

  const exportToPDF = () => {
    toast.success('سيتم تطوير ميزة التصدير إلى PDF قريباً');
  };

  const exportToExcel = () => {
    toast.success('سيتم تطوير ميزة التصدير إلى Excel قريباً');
  };

  if (loading) {
    return (
      <Layout>
        <div className="p-6">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-dark-700 rounded w-1/4"></div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-64 bg-dark-700 rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (!stats) {
    return (
      <Layout>
        <div className="p-6">
          <div className="text-center text-gray-400">
            فشل في تحميل بيانات التقارير
          </div>
        </div>
      </Layout>
    );
  }

  // Prepare chart data
  const severityData = Object.entries(stats.incidents_by_severity).map(([key, value]) => ({
    name: formatSeverity(key),
    value,
    color: getSeverityColor(key)
  }));

  const statusData = Object.entries(stats.incidents_by_status).map(([key, value]) => ({
    name: formatStatus(key),
    value,
    color: getStatusColor(key)
  }));

  const typeData = Object.entries(stats.incidents_by_type)
    .filter(([_, value]) => value > 0)
    .map(([key, value]) => ({
      name: formatIncidentType(key),
      value,
      color: getSeverityColor('medium')
    }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 8);

  return (
    <Layout>
      <div className="min-h-screen bg-dark-950 p-6">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Header */}
          <div className="bg-dark-800 border border-dark-700 rounded-xl p-6 shadow-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="bg-blue-600 p-3 rounded-xl shadow-lg">
                  <ChartBarIcon className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-100 mb-1">
                    تقارير الحوادث الأمنية
                  </h1>
                  <p className="text-gray-400 text-lg">
                    تحليل شامل وإحصائيات تفصيلية للحوادث الأمنية
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <button
                  onClick={exportToPDF}
                  className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  <DocumentTextIcon className="h-5 w-5 mr-2" />
                  تصدير PDF
                </button>
                <button
                  onClick={exportToExcel}
                  className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
                  تصدير Excel
                </button>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-dark-800 border border-dark-700 rounded-xl p-6 shadow-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 space-x-reverse">
                <FunnelIcon className="h-6 w-6 text-gray-400" />
                <h3 className="text-lg font-semibold text-gray-100">فلاتر التقارير</h3>
              </div>
              <div className="flex items-center space-x-4 space-x-reverse">
                <select
                  value={selectedFilter}
                  onChange={(e) => setSelectedFilter(e.target.value)}
                  className="input-field"
                >
                  <option value="all">جميع الحوادث</option>
                  <option value="critical">الحوادث الحرجة فقط</option>
                  <option value="open">الحوادث المفتوحة فقط</option>
                  <option value="recent">آخر 30 يوم</option>
                </select>
                <input
                  type="date"
                  value={dateRange.startDate}
                  onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
                  className="input-field"
                />
                <input
                  type="date"
                  value={dateRange.endDate}
                  onChange={(e) => setDateRange({...dateRange, endDate: e.target.value})}
                  className="input-field"
                />
              </div>
            </div>
          </div>

          {/* Charts Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Severity Distribution */}
            <div className="bg-dark-800 border border-dark-700 rounded-xl shadow-lg">
              <div className="p-6 border-b border-dark-700">
                <h3 className="text-xl font-bold text-gray-100 mb-2">توزيع الحوادث حسب الخطورة</h3>
                <p className="text-gray-400 text-sm">تصنيف الحوادث حسب مستوى الخطورة</p>
              </div>
              <div className="p-6">
                <ResponsiveContainer width="100%" height={350}>
                  <PieChart>
                    <Pie
                      data={severityData}
                      cx="50%"
                      cy="45%"
                      outerRadius={100}
                      innerRadius={40}
                      dataKey="value"
                      stroke="#1e293b"
                      strokeWidth={2}
                    >
                      {severityData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      contentStyle={{
                        backgroundColor: '#1e293b',
                        border: '1px solid #334155',
                        borderRadius: '8px',
                        color: '#f1f5f9'
                      }}
                      formatter={(value, name) => [`${value} حادث`, name]}
                    />
                    <Legend
                      verticalAlign="bottom"
                      height={36}
                      wrapperStyle={{ color: '#94a3b8', fontSize: '14px' }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Status Distribution */}
            <div className="bg-dark-800 border border-dark-700 rounded-xl shadow-lg">
              <div className="p-6 border-b border-dark-700">
                <h3 className="text-xl font-bold text-gray-100 mb-2">توزيع الحوادث حسب الحالة</h3>
                <p className="text-gray-400 text-sm">حالة معالجة الحوادث الأمنية</p>
              </div>
              <div className="p-6">
                <ResponsiveContainer width="100%" height={350}>
                  <PieChart>
                    <Pie
                      data={statusData}
                      cx="50%"
                      cy="45%"
                      outerRadius={100}
                      innerRadius={40}
                      dataKey="value"
                      stroke="#1e293b"
                      strokeWidth={2}
                    >
                      {statusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      contentStyle={{
                        backgroundColor: '#1e293b',
                        border: '1px solid #334155',
                        borderRadius: '8px',
                        color: '#f1f5f9'
                      }}
                      formatter={(value, name) => [`${value} حادث`, name]}
                    />
                    <Legend
                      verticalAlign="bottom"
                      height={36}
                      wrapperStyle={{ color: '#94a3b8', fontSize: '14px' }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>

          {/* Horizontal Bar Chart for Incident Types */}
          <div className="bg-dark-800 border border-dark-700 rounded-xl shadow-lg">
            <div className="p-6 border-b border-dark-700">
              <h3 className="text-xl font-bold text-gray-100 mb-2">أنواع الحوادث الأكثر شيوعاً</h3>
              <p className="text-gray-400 text-sm">إحصائيات تفصيلية لأنواع الحوادث الأمنية (محاور أفقية)</p>
            </div>
            <div className="p-6">
              <ResponsiveContainer width="100%" height={400}>
                <BarChart
                  data={typeData}
                  layout="horizontal"
                  margin={{ top: 20, right: 30, left: 100, bottom: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#334155" opacity={0.3} />
                  <XAxis
                    type="number"
                    stroke="#94a3b8"
                    fontSize={12}
                    tick={{ fill: '#94a3b8' }}
                  />
                  <YAxis
                    type="category"
                    dataKey="name"
                    stroke="#94a3b8"
                    fontSize={12}
                    tick={{ fill: '#94a3b8' }}
                    width={120}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#1e293b',
                      border: '1px solid #334155',
                      borderRadius: '8px',
                      color: '#f1f5f9'
                    }}
                    formatter={(value, name) => [`${value} حادث`, 'العدد']}
                    labelFormatter={(label) => `نوع الحادث: ${label}`}
                  />
                  <Bar
                    dataKey="value"
                    radius={[0, 4, 4, 0]}
                    stroke="#1e40af"
                    strokeWidth={1}
                  >
                    {typeData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={`hsl(${220 + index * 25}, 70%, ${60 + index * 3}%)`}
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Reports;
