{"c": ["pages/_app", "webpack"], "r": ["/_error", "pages/incidents"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=E%3A%5CIR-Platform%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!", "./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=E%3A%5CIR-Platform%5Cfrontend%5Csrc%5Cpages%5Cincidents%5Cindex.tsx&page=%2Fincidents!", "./src/components/incidents/IncidentList.tsx", "./src/pages/incidents/index.tsx", "__barrel_optimize__?names=EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js"]}