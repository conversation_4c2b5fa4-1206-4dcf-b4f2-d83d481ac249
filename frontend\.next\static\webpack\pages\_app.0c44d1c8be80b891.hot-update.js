"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; },\n/* harmony export */   withAuth: function() { return /* binding */ withAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/api */ \"./src/utils/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Check if user is authenticated and fetch user data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initAuth = async ()=>{\n            if (_utils_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAuthenticated()) {\n                try {\n                    await refreshUser();\n                } catch (error) {\n                    console.error(\"Failed to fetch user data:\", error);\n                    logout();\n                }\n            }\n            setLoading(false);\n        };\n        initAuth();\n    }, []);\n    const login = async (credentials)=>{\n        try {\n            setLoading(true);\n            const tokens = await _utils_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/auth/login/\", credentials);\n            // Store tokens\n            _utils_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].setTokens(tokens);\n            // Fetch user data\n            await refreshUser();\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"تم تسجيل الدخول بنجاح\");\n            // Redirect to dashboard\n            router.push(\"/dashboard\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(error.detail || \"فشل في تسجيل الدخول\");\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = ()=>{\n        _utils_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].clearAuth();\n        setUser(null);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"تم تسجيل الخروج بنجاح\");\n        router.push(\"/login\");\n    };\n    const refreshUser = async ()=>{\n        try {\n            const userData = await _utils_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/auth/me/\");\n            setUser(userData);\n        } catch (error) {\n            throw error;\n        }\n    };\n    const hasRole = (roles)=>{\n        if (!user) return false;\n        const roleArray = Array.isArray(roles) ? roles : [\n            roles\n        ];\n        return roleArray.includes(user.role);\n    };\n    const value = {\n        user,\n        loading,\n        login,\n        logout,\n        refreshUser,\n        isAuthenticated: !!user,\n        hasRole\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AuthProvider, \"J17Kp8z+0ojgAqGoY5o3BCjwWms=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\nconst useAuth = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Higher-order component for protected routes\nconst withAuth = (WrappedComponent, requiredRoles)=>{\n    var _s = $RefreshSig$();\n    const AuthenticatedComponent = (props)=>{\n        _s();\n        const { user, loading } = useAuth();\n        const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (!loading) {\n                if (!user) {\n                    router.push(\"/login\");\n                    return;\n                }\n                if (requiredRoles && !requiredRoles.includes(user.role)) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(\"ليس لديك صلاحية للوصول إلى هذه الصفحة\");\n                    router.push(\"/dashboard\");\n                    return;\n                }\n            }\n        }, [\n            user,\n            loading,\n            router\n        ]);\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"spinner w-8 h-8\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (!user) {\n            return null;\n        }\n        if (requiredRoles && !requiredRoles.includes(user.role)) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n            ...props\n        }, void 0, false, {\n            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 156,\n            columnNumber: 12\n        }, undefined);\n    };\n    _s(AuthenticatedComponent, \"Zr2WDa/YWeMetzDhcnOimt0LiKE=\", false, function() {\n        return [\n            useAuth,\n            next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n        ];\n    });\n    AuthenticatedComponent.displayName = \"withAuth(\".concat(WrappedComponent.displayName || WrappedComponent.name, \")\");\n    return AuthenticatedComponent;\n};\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/AuthContext.tsx\n"));

/***/ })

});