"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "./src/components/dashboard/StatsCard.tsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/StatsCard.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon!=!@heroicons/react/24/solid */ \"__barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon!=!./node_modules/@heroicons/react/24/solid/esm/index.js\");\n\n\n\nconst StatsCard = (param)=>{\n    let { title, value, icon: Icon, color, trend } = param;\n    const colorClasses = {\n        blue: \"bg-blue-600 text-blue-100\",\n        red: \"bg-red-600 text-red-100\",\n        yellow: \"bg-yellow-600 text-yellow-100\",\n        green: \"bg-green-600 text-green-100\",\n        purple: \"bg-purple-600 text-purple-100\"\n    };\n    const bgColorClasses = {\n        blue: \"bg-blue-50 dark:bg-blue-900/20\",\n        red: \"bg-red-50 dark:bg-red-900/20\",\n        yellow: \"bg-yellow-50 dark:bg-yellow-900/20\",\n        green: \"bg-green-50 dark:bg-green-900/20\",\n        purple: \"bg-purple-50 dark:bg-purple-900/20\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 rounded-lg \".concat(colorClasses[color]),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-4 flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-400\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-baseline\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-semibold text-gray-100\",\n                                        children: value.toLocaleString()\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-2 flex items-center text-sm \".concat(trend.isPositive ? \"text-green-400\" : \"text-red-400\"),\n                                        children: [\n                                            trend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__.ArrowUpIcon, {\n                                                className: \"h-4 w-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__.ArrowDownIcon, {\n                                                className: \"h-4 w-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            Math.abs(trend.value),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n_c = StatsCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (StatsCard);\nvar _c;\n$RefreshReg$(_c, \"StatsCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/dashboard/StatsCard.tsx\n"));

/***/ })

});