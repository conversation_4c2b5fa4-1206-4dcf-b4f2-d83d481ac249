{"name": "ir-platform-frontend", "version": "1.0.0", "description": "Cybersecurity Incident Reporting System Frontend", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.2.0", "@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "axios": "^1.6.0", "react-hook-form": "^7.47.0", "react-query": "^3.39.3", "js-cookie": "^3.0.5", "@types/js-cookie": "^3.0.6", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-datepicker": "^4.21.0", "@types/react-datepicker": "^4.19.0", "clsx": "^2.0.0", "lucide-react": "^0.292.0"}, "devDependencies": {"eslint": "^8.52.0", "eslint-config-next": "^14.0.0", "@tailwindcss/forms": "^0.5.7"}}