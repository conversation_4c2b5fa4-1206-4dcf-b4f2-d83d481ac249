# Application Configuration
APP_NAME="Cybersecurity Incident Reporting System"
VERSION="1.0.0"
DEBUG=True

# Database Configuration
DATABASE_URL="sqlite:///./database.db"

# Security Configuration
SECRET_KEY="your-super-secret-key-change-this-in-production"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Password Requirements
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=True
PASSWORD_REQUIRE_LOWERCASE=True
PASSWORD_REQUIRE_NUMBERS=True
PASSWORD_REQUIRE_SPECIAL=True

# Rate Limiting
RATE_LIMIT_LOGIN="5/minute"
RATE_LIMIT_API="100/minute"

# CORS Settings
ALLOWED_HOSTS="http://localhost:3000,http://127.0.0.1:3000,http://localhost:8000,http://127.0.0.1:8000"

# File Upload Settings
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR="uploads"
