"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,ChartBarIcon,Cog6ToothIcon,HomeIcon,ShieldCheckIcon,UserCircleIcon,UsersIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,ChartBarIcon,Cog6ToothIcon,HomeIcon,ShieldCheckIcon,UserCircleIcon,UsersIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \**********************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowRightOnRectangleIcon: function() { return /* reexport safe */ _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   Bars3Icon: function() { return /* reexport safe */ _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   ChartBarIcon: function() { return /* reexport safe */ _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   Cog6ToothIcon: function() { return /* reexport safe */ _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; },\n/* harmony export */   HomeIcon: function() { return /* reexport safe */ _HomeIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]; },\n/* harmony export */   ShieldCheckIcon: function() { return /* reexport safe */ _ShieldCheckIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]; },\n/* harmony export */   UserCircleIcon: function() { return /* reexport safe */ _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]; },\n/* harmony export */   UsersIcon: function() { return /* reexport safe */ _UsersIcon_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]; },\n/* harmony export */   XMarkIcon: function() { return /* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowRightOnRectangleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Bars3Icon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChartBarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Cog6ToothIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _HomeIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./HomeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _ShieldCheckIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ShieldCheckIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./UserCircleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n/* harmony import */ var _UsersIcon_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./UsersIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./XMarkIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd1JpZ2h0T25SZWN0YW5nbGVJY29uLEJhcnMzSWNvbixDaGFydEJhckljb24sQ29nNlRvb3RoSWNvbixIb21lSWNvbixTaGllbGRDaGVja0ljb24sVXNlckNpcmNsZUljb24sVXNlcnNJY29uLFhNYXJrSWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNxRjtBQUNoQztBQUNNO0FBQ0U7QUFDVjtBQUNjO0FBQ0Y7QUFDViIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz9jOTg0Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcnJvd1JpZ2h0T25SZWN0YW5nbGVJY29uIH0gZnJvbSBcIi4vQXJyb3dSaWdodE9uUmVjdGFuZ2xlSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJhcnMzSWNvbiB9IGZyb20gXCIuL0JhcnMzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoYXJ0QmFySWNvbiB9IGZyb20gXCIuL0NoYXJ0QmFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENvZzZUb290aEljb24gfSBmcm9tIFwiLi9Db2c2VG9vdGhJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSG9tZUljb24gfSBmcm9tIFwiLi9Ib21lSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNoaWVsZENoZWNrSWNvbiB9IGZyb20gXCIuL1NoaWVsZENoZWNrSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVzZXJDaXJjbGVJY29uIH0gZnJvbSBcIi4vVXNlckNpcmNsZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2Vyc0ljb24gfSBmcm9tIFwiLi9Vc2Vyc0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBYTWFya0ljb24gfSBmcm9tIFwiLi9YTWFya0ljb24uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,ChartBarIcon,Cog6ToothIcon,HomeIcon,ShieldCheckIcon,UserCircleIcon,UsersIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ }),

/***/ "__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!./node_modules/recharts/es6/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!./node_modules/recharts/es6/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bar: function() { return /* reexport safe */ _cartesian_Bar__WEBPACK_IMPORTED_MODULE_0__.Bar; },\n/* harmony export */   BarChart: function() { return /* reexport safe */ _chart_BarChart__WEBPACK_IMPORTED_MODULE_1__.BarChart; },\n/* harmony export */   CartesianGrid: function() { return /* reexport safe */ _cartesian_CartesianGrid__WEBPACK_IMPORTED_MODULE_2__.CartesianGrid; },\n/* harmony export */   Cell: function() { return /* reexport safe */ _component_Cell__WEBPACK_IMPORTED_MODULE_3__.Cell; },\n/* harmony export */   Legend: function() { return /* reexport safe */ _component_Legend__WEBPACK_IMPORTED_MODULE_4__.Legend; },\n/* harmony export */   Pie: function() { return /* reexport safe */ _polar_Pie__WEBPACK_IMPORTED_MODULE_5__.Pie; },\n/* harmony export */   PieChart: function() { return /* reexport safe */ _chart_PieChart__WEBPACK_IMPORTED_MODULE_6__.PieChart; },\n/* harmony export */   ResponsiveContainer: function() { return /* reexport safe */ _component_ResponsiveContainer__WEBPACK_IMPORTED_MODULE_7__.ResponsiveContainer; },\n/* harmony export */   Tooltip: function() { return /* reexport safe */ _component_Tooltip__WEBPACK_IMPORTED_MODULE_8__.Tooltip; },\n/* harmony export */   XAxis: function() { return /* reexport safe */ _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_9__.XAxis; },\n/* harmony export */   YAxis: function() { return /* reexport safe */ _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_10__.YAxis; }\n/* harmony export */ });\n/* harmony import */ var _cartesian_Bar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cartesian/Bar */ \"./node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _chart_BarChart__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chart/BarChart */ \"./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _cartesian_CartesianGrid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cartesian/CartesianGrid */ \"./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _component_Cell__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./component/Cell */ \"./node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _component_Legend__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./component/Legend */ \"./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _polar_Pie__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./polar/Pie */ \"./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _chart_PieChart__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./chart/PieChart */ \"./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _component_ResponsiveContainer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./component/ResponsiveContainer */ \"./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _component_Tooltip__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./component/Tooltip */ \"./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./cartesian/XAxis */ \"./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./cartesian/YAxis */ \"./node_modules/recharts/es6/cartesian/YAxis.js\");\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXIsQmFyQ2hhcnQsQ2FydGVzaWFuR3JpZCxDZWxsLExlZ2VuZCxQaWUsUGllQ2hhcnQsUmVzcG9uc2l2ZUNvbnRhaW5lcixUb29sdGlwLFhBeGlzLFlBeGlzIT0hLi9ub2RlX21vZHVsZXMvcmVjaGFydHMvZXM2L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ3FDO0FBQ007QUFDYztBQUNsQjtBQUNJO0FBQ1Y7QUFDVTtBQUMwQjtBQUN4QjtBQUNKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9yZWNoYXJ0cy9lczYvaW5kZXguanM/MjI2ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IEJhciB9IGZyb20gXCIuL2NhcnRlc2lhbi9CYXJcIlxuZXhwb3J0IHsgQmFyQ2hhcnQgfSBmcm9tIFwiLi9jaGFydC9CYXJDaGFydFwiXG5leHBvcnQgeyBDYXJ0ZXNpYW5HcmlkIH0gZnJvbSBcIi4vY2FydGVzaWFuL0NhcnRlc2lhbkdyaWRcIlxuZXhwb3J0IHsgQ2VsbCB9IGZyb20gXCIuL2NvbXBvbmVudC9DZWxsXCJcbmV4cG9ydCB7IExlZ2VuZCB9IGZyb20gXCIuL2NvbXBvbmVudC9MZWdlbmRcIlxuZXhwb3J0IHsgUGllIH0gZnJvbSBcIi4vcG9sYXIvUGllXCJcbmV4cG9ydCB7IFBpZUNoYXJ0IH0gZnJvbSBcIi4vY2hhcnQvUGllQ2hhcnRcIlxuZXhwb3J0IHsgUmVzcG9uc2l2ZUNvbnRhaW5lciB9IGZyb20gXCIuL2NvbXBvbmVudC9SZXNwb25zaXZlQ29udGFpbmVyXCJcbmV4cG9ydCB7IFRvb2x0aXAgfSBmcm9tIFwiLi9jb21wb25lbnQvVG9vbHRpcFwiXG5leHBvcnQgeyBYQXhpcyB9IGZyb20gXCIuL2NhcnRlc2lhbi9YQXhpc1wiXG5leHBvcnQgeyBZQXhpcyB9IGZyb20gXCIuL2NhcnRlc2lhbi9ZQXhpc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!./node_modules/recharts/es6/index.js\n"));

/***/ }),

/***/ "__barrel_optimize__?names=ChartBarIcon,CheckCircleIcon,ClockIcon,ExclamationCircleIcon,FireIcon,ShieldCheckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*********************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChartBarIcon,CheckCircleIcon,ClockIcon,ExclamationCircleIcon,FireIcon,ShieldCheckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*********************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChartBarIcon: function() { return /* reexport safe */ _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   CheckCircleIcon: function() { return /* reexport safe */ _CheckCircleIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   ClockIcon: function() { return /* reexport safe */ _ClockIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   ExclamationCircleIcon: function() { return /* reexport safe */ _ExclamationCircleIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; },\n/* harmony export */   FireIcon: function() { return /* reexport safe */ _FireIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]; },\n/* harmony export */   ShieldCheckIcon: function() { return /* reexport safe */ _ShieldCheckIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ChartBarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _CheckCircleIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CheckCircleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _ClockIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ClockIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _ExclamationCircleIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ExclamationCircleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ExclamationCircleIcon.js\");\n/* harmony import */ var _FireIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FireIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/FireIcon.js\");\n/* harmony import */ var _ShieldCheckIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ShieldCheckIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGFydEJhckljb24sQ2hlY2tDaXJjbGVJY29uLENsb2NrSWNvbixFeGNsYW1hdGlvbkNpcmNsZUljb24sRmlyZUljb24sU2hpZWxkQ2hlY2tJY29uIT0hLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQzJEO0FBQ007QUFDWjtBQUN3QjtBQUMxQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz9kNmRmIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGFydEJhckljb24gfSBmcm9tIFwiLi9DaGFydEJhckljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGVja0NpcmNsZUljb24gfSBmcm9tIFwiLi9DaGVja0NpcmNsZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDbG9ja0ljb24gfSBmcm9tIFwiLi9DbG9ja0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBFeGNsYW1hdGlvbkNpcmNsZUljb24gfSBmcm9tIFwiLi9FeGNsYW1hdGlvbkNpcmNsZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBGaXJlSWNvbiB9IGZyb20gXCIuL0ZpcmVJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2hpZWxkQ2hlY2tJY29uIH0gZnJvbSBcIi4vU2hpZWxkQ2hlY2tJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ChartBarIcon,CheckCircleIcon,ClockIcon,ExclamationCircleIcon,FireIcon,ShieldCheckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ }),

/***/ "./src/components/dashboard/Dashboard.tsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/Dashboard.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckCircleIcon_ClockIcon_ExclamationCircleIcon_FireIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckCircleIcon,ClockIcon,ExclamationCircleIcon,FireIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ChartBarIcon,CheckCircleIcon,ClockIcon,ExclamationCircleIcon,FireIcon,ShieldCheckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!./node_modules/recharts/es6/index.js\");\n/* harmony import */ var _StatsCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./StatsCard */ \"./src/components/dashboard/StatsCard.tsx\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/api */ \"./src/utils/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Dashboard = ()=>{\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentIncidents, setRecentIncidents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchDashboardData();\n    }, []);\n    const fetchDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            const [statsData, incidentsData] = await Promise.all([\n                _utils_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/incidents/stats/dashboard/\"),\n                _utils_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/incidents/?limit=5\")\n            ]);\n            setStats(statsData);\n            setRecentIncidents(incidentsData);\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(\"فشل في تحميل بيانات لوحة التحكم\");\n            console.error(\"Dashboard data fetch error:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getSeverityColor = (severity)=>{\n        switch(severity.toLowerCase()){\n            case \"critical\":\n                return \"#ef4444\"; // أحمر فاتح للحرج\n            case \"high\":\n                return \"#f97316\"; // برتقالي فاتح للعالي\n            case \"medium\":\n                return \"#eab308\"; // أصفر فاتح للمتوسط\n            case \"low\":\n                return \"#22c55e\"; // أخضر فاتح للمنخفض\n            default:\n                return \"#94a3b8\"; // رمادي فاتح\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case \"open\":\n                return \"#ef4444\"; // أحمر فاتح للمفتوح\n            case \"in_progress\":\n                return \"#f59e0b\"; // برتقالي فاتح لقيد المعالجة\n            case \"under_investigation\":\n                return \"#3b82f6\"; // أزرق فاتح لقيد التحقيق\n            case \"closed\":\n                return \"#10b981\"; // أخضر فاتح للمغلق\n            default:\n                return \"#94a3b8\"; // رمادي فاتح\n        }\n    };\n    const formatIncidentType = (type)=>{\n        const typeMap = {\n            \"MALWARE_INFECTION\": \"إصابة بالبرمجيات الخبيثة\",\n            \"PHISHING_ATTACK\": \"هجوم تصيد\",\n            \"DATA_BREACH\": \"تسريب بيانات\",\n            \"UNAUTHORIZED_ACCESS\": \"وصول غير مصرح\",\n            \"DDOS_ATTACK\": \"هجوم حجب الخدمة\",\n            \"INSIDER_THREAT\": \"تهديد داخلي\",\n            \"SYSTEM_COMPROMISE\": \"اختراق النظام\",\n            \"NETWORK_INTRUSION\": \"تسلل الشبكة\",\n            \"SOCIAL_ENGINEERING\": \"هندسة اجتماعية\",\n            \"PHYSICAL_SECURITY_BREACH\": \"خرق الأمان المادي\",\n            \"OTHER\": \"أخرى\"\n        };\n        return typeMap[type] || type;\n    };\n    const formatStatus = (status)=>{\n        const statusMap = {\n            \"OPEN\": \"مفتوح\",\n            \"IN_PROGRESS\": \"قيد المعالجة\",\n            \"UNDER_INVESTIGATION\": \"قيد التحقيق\",\n            \"CLOSED\": \"مغلق\"\n        };\n        return statusMap[status] || status;\n    };\n    const formatSeverity = (severity)=>{\n        const severityMap = {\n            \"CRITICAL\": \"حرج\",\n            \"HIGH\": \"عالي\",\n            \"MEDIUM\": \"متوسط\",\n            \"LOW\": \"منخفض\"\n        };\n        return severityMap[severity] || severity;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-body\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-16 bg-dark-700 rounded\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 17\n                            }, undefined)\n                        }, i, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!stats) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-gray-400\",\n                children: \"فشل في تحميل بيانات لوحة التحكم\"\n            }, void 0, false, {\n                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Prepare chart data\n    const severityData = Object.entries(stats.incidents_by_severity).map((param)=>{\n        let [key, value] = param;\n        return {\n            name: formatSeverity(key),\n            value,\n            color: getSeverityColor(key)\n        };\n    });\n    const statusData = Object.entries(stats.incidents_by_status).map((param)=>{\n        let [key, value] = param;\n        return {\n            name: formatStatus(key),\n            value,\n            color: getStatusColor(key)\n        };\n    });\n    const typeData = Object.entries(stats.incidents_by_type).filter((param)=>{\n        let [_, value] = param;\n        return value > 0;\n    }).map((param)=>{\n        let [key, value] = param;\n        return {\n            name: formatIncidentType(key),\n            value\n        };\n    }).sort((a, b)=>b.value - a.value).slice(0, 6);\n    // Get current date and time in Arabic\n    const getCurrentDateTime = ()=>{\n        const now = new Date();\n        const arabicDate = now.toLocaleDateString(\"ar-SA\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            weekday: \"long\"\n        });\n        const arabicTime = now.toLocaleTimeString(\"ar-SA\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: false\n        });\n        return \"\".concat(arabicDate, \" - \").concat(arabicTime);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-dark-950 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-dark-800 border border-dark-700 rounded-xl p-6 shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-primary-600 p-3 rounded-xl shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckCircleIcon_ClockIcon_ExclamationCircleIcon_FireIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ShieldCheckIcon, {\n                                            className: \"h-8 w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-100 mb-1\",\n                                                children: \"نظام إدارة الحوادث الأمنية\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-lg\",\n                                                children: \"لوحة التحكم الرئيسية - نظرة شاملة على الحوادث الأمنية\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-sm text-gray-400 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckCircleIcon_ClockIcon_ExclamationCircleIcon_FireIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ClockIcon, {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"آخر تحديث\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 font-medium\",\n                                        children: getCurrentDateTime()\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            title: \"إجمالي الحوادث\",\n                            value: stats.total_incidents,\n                            icon: _barrel_optimize_names_ChartBarIcon_CheckCircleIcon_ClockIcon_ExclamationCircleIcon_FireIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChartBarIcon,\n                            color: \"blue\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            title: \"الحوادث المفتوحة\",\n                            value: stats.open_incidents,\n                            icon: _barrel_optimize_names_ChartBarIcon_CheckCircleIcon_ClockIcon_ExclamationCircleIcon_FireIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ExclamationCircleIcon,\n                            color: \"red\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            title: \"الحوادث الحرجة\",\n                            value: stats.critical_incidents,\n                            icon: _barrel_optimize_names_ChartBarIcon_CheckCircleIcon_ClockIcon_ExclamationCircleIcon_FireIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.FireIcon,\n                            color: \"purple\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            title: \"الحوادث المغلقة\",\n                            value: stats.closed_incidents,\n                            icon: _barrel_optimize_names_ChartBarIcon_CheckCircleIcon_ClockIcon_ExclamationCircleIcon_FireIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CheckCircleIcon,\n                            color: \"green\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-dark-800 border border-dark-700 rounded-xl shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-dark-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-100 mb-2\",\n                                            children: \"توزيع الحوادث حسب الخطورة\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"تصنيف الحوادث حسب مستوى الخطورة\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 350,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Pie, {\n                                                    data: severityData,\n                                                    cx: \"50%\",\n                                                    cy: \"45%\",\n                                                    outerRadius: 100,\n                                                    innerRadius: 40,\n                                                    dataKey: \"value\",\n                                                    stroke: \"#1e293b\",\n                                                    strokeWidth: 2,\n                                                    children: severityData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Cell, {\n                                                            fill: entry.color\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Tooltip, {\n                                                    contentStyle: {\n                                                        backgroundColor: \"#1e293b\",\n                                                        border: \"1px solid #334155\",\n                                                        borderRadius: \"8px\",\n                                                        color: \"#f1f5f9\"\n                                                    },\n                                                    formatter: (value, name)=>[\n                                                            \"\".concat(value, \" حادث\"),\n                                                            name\n                                                        ]\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Legend, {\n                                                    verticalAlign: \"bottom\",\n                                                    height: 36,\n                                                    wrapperStyle: {\n                                                        color: \"#94a3b8\",\n                                                        fontSize: \"14px\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-dark-800 border border-dark-700 rounded-xl shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-dark-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-100 mb-2\",\n                                            children: \"توزيع الحوادث حسب الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"حالة معالجة الحوادث الأمنية\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 350,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Pie, {\n                                                    data: statusData,\n                                                    cx: \"50%\",\n                                                    cy: \"45%\",\n                                                    outerRadius: 100,\n                                                    innerRadius: 40,\n                                                    dataKey: \"value\",\n                                                    stroke: \"#1e293b\",\n                                                    strokeWidth: 2,\n                                                    children: statusData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Cell, {\n                                                            fill: entry.color\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Tooltip, {\n                                                    contentStyle: {\n                                                        backgroundColor: \"#1e293b\",\n                                                        border: \"1px solid #334155\",\n                                                        borderRadius: \"8px\",\n                                                        color: \"#f1f5f9\"\n                                                    },\n                                                    formatter: (value, name)=>[\n                                                            \"\".concat(value, \" حادث\"),\n                                                            name\n                                                        ]\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Legend, {\n                                                    verticalAlign: \"bottom\",\n                                                    height: 36,\n                                                    wrapperStyle: {\n                                                        color: \"#94a3b8\",\n                                                        fontSize: \"14px\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-dark-800 border border-dark-700 rounded-xl shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-dark-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-100 mb-2\",\n                                    children: \"أنواع الحوادث الأكثر شيوعاً\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: \"إحصائيات تفصيلية لأنواع الحوادث الأمنية\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.ResponsiveContainer, {\n                                width: \"100%\",\n                                height: 400,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.BarChart, {\n                                    data: typeData,\n                                    margin: {\n                                        top: 20,\n                                        right: 30,\n                                        left: 20,\n                                        bottom: 100\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.CartesianGrid, {\n                                            strokeDasharray: \"3 3\",\n                                            stroke: \"#334155\",\n                                            opacity: 0.3\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.XAxis, {\n                                            dataKey: \"name\",\n                                            stroke: \"#94a3b8\",\n                                            fontSize: 12,\n                                            angle: -45,\n                                            textAnchor: \"end\",\n                                            height: 100,\n                                            interval: 0,\n                                            tick: {\n                                                fill: \"#94a3b8\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.YAxis, {\n                                            stroke: \"#94a3b8\",\n                                            fontSize: 12,\n                                            tick: {\n                                                fill: \"#94a3b8\"\n                                            },\n                                            label: {\n                                                value: \"عدد الحوادث\",\n                                                angle: -90,\n                                                position: \"insideLeft\",\n                                                style: {\n                                                    textAnchor: \"middle\",\n                                                    fill: \"#94a3b8\"\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Tooltip, {\n                                            contentStyle: {\n                                                backgroundColor: \"#1e293b\",\n                                                border: \"1px solid #334155\",\n                                                borderRadius: \"8px\",\n                                                color: \"#f1f5f9\"\n                                            },\n                                            formatter: (value, name)=>[\n                                                    \"\".concat(value, \" حادث\"),\n                                                    \"العدد\"\n                                                ],\n                                            labelFormatter: (label)=>\"نوع الحادث: \".concat(label)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Bar, {\n                                            dataKey: \"value\",\n                                            radius: [\n                                                6,\n                                                6,\n                                                0,\n                                                0\n                                            ],\n                                            stroke: \"#1e40af\",\n                                            strokeWidth: 1,\n                                            children: typeData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Cell, {\n                                                    fill: \"hsl(\".concat(220 + index * 30, \", 70%, \").concat(60 + index * 5, \"%)\")\n                                                }, \"cell-\".concat(index), false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-dark-800 border border-dark-700 rounded-xl shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-dark-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-100 mb-2\",\n                                    children: \"الحوادث الأخيرة\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: \"آخر الحوادث المسجلة في النظام\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: recentIncidents.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: recentIncidents.map((incident)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-5 bg-dark-700 border border-dark-600 rounded-lg hover:bg-dark-600 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-100 mb-2 text-lg\",\n                                                        children: incident.incident_title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400 mb-1\",\n                                                        children: [\n                                                            formatIncidentType(incident.incident_type),\n                                                            \" • \",\n                                                            incident.affected_system\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: new Date(incident.incident_date).toLocaleDateString(\"ar-SA\")\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"badge badge-\".concat(incident.severity_level.toLowerCase(), \" text-sm font-medium\"),\n                                                        children: formatSeverity(incident.severity_level)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"badge badge-\".concat(incident.status.toLowerCase().replace(\"_\", \"-\"), \" text-sm font-medium\"),\n                                                        children: formatStatus(incident.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, incident.id, true, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-gray-400 py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckCircleIcon_ClockIcon_ExclamationCircleIcon_FireIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ExclamationCircleIcon, {\n                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium\",\n                                        children: \"لا توجد حوادث حديثة\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: \"سيتم عرض الحوادث الجديدة هنا عند إضافتها\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n            lineNumber: 171,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Dashboard, \"Q5LdgZ+R63+eF+7ajBOguc5/oDA=\");\n_c = Dashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/dashboard/Dashboard.tsx\n"));

/***/ }),

/***/ "./src/components/layout/Layout.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Layout.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,ChartBarIcon,Cog6ToothIcon,HomeIcon,ShieldCheckIcon,UserCircleIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,ChartBarIcon,Cog6ToothIcon,HomeIcon,ShieldCheckIcon,UserCircleIcon,UsersIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst Layout = (param)=>{\n    let { children } = param;\n    _s();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout, hasRole } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const navigation = [\n        {\n            name: \"لوحة التحكم\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.HomeIcon,\n            current: router.pathname === \"/dashboard\"\n        },\n        {\n            name: \"الحوادث الأمنية\",\n            href: \"/incidents\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShieldCheckIcon,\n            current: router.pathname.startsWith(\"/incidents\")\n        },\n        {\n            name: \"إدارة المستخدمين\",\n            href: \"/users\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.UsersIcon,\n            current: router.pathname.startsWith(\"/users\"),\n            requiredRoles: [\n                \"admin\"\n            ]\n        },\n        {\n            name: \"التقارير\",\n            href: \"/reports\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChartBarIcon,\n            current: router.pathname.startsWith(\"/reports\")\n        },\n        {\n            name: \"الإعدادات\",\n            href: \"/settings\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.Cog6ToothIcon,\n            current: router.pathname.startsWith(\"/settings\")\n        }\n    ];\n    const filteredNavigation = navigation.filter((item)=>!item.requiredRoles || hasRole(item.requiredRoles));\n    const handleLogout = ()=>{\n        logout();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-dark-950\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 lg:hidden \".concat(sidebarOpen ? \"block\" : \"hidden\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-y-0 right-0 w-64 bg-dark-900 shadow-xl border-l border-dark-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between h-20 px-6 border-b border-dark-700 bg-dark-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-primary-600 p-2 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShieldCheckIcon, {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-bold text-white\",\n                                                        children: \"نظام الحوادث الأمنية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: \"إدارة الأمن السيبراني\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSidebarOpen(false),\n                                        className: \"text-gray-400 hover:text-white transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.XMarkIcon, {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"mt-6 px-4 space-y-2\",\n                                children: filteredNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: item.href,\n                                        className: \"sidebar-link \".concat(item.current ? \"active\" : \"\"),\n                                        onClick: ()=>setSidebarOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"h-5 w-5 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            item.name\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:right-0 lg:w-64 lg:block\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full bg-dark-900 border-l border-dark-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center h-20 px-6 border-b border-dark-700 bg-dark-800\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-primary-600 p-2 rounded-lg shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShieldCheckIcon, {\n                                            className: \"h-7 w-7 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-bold text-white\",\n                                                children: \"نظام الحوادث الأمنية\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"إدارة الأمن السيبراني\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 mt-6 px-4 space-y-2\",\n                            children: filteredNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: item.href,\n                                    className: \"sidebar-link \".concat(item.current ? \"active\" : \"\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"h-5 w-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-dark-700 p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.UserCircleIcon, {\n                                            className: \"h-8 w-8 text-gray-400 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-white\",\n                                                    children: user === null || user === void 0 ? void 0 : user.username\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: (user === null || user === void 0 ? void 0 : user.role) === \"admin\" ? \"مدير\" : (user === null || user === void 0 ? void 0 : user.role) === \"analyst\" ? \"محلل أمني\" : \"مشاهد\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogout,\n                                    className: \"flex items-center w-full px-3 py-2 text-sm text-gray-300 hover:bg-dark-700 hover:text-white rounded-lg transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ArrowRightOnRectangleIcon, {\n                                            className: \"h-5 w-5 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"تسجيل الخروج\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:mr-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-40 bg-dark-900 border-b border-dark-700 lg:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-lg font-semibold text-white\",\n                                    children: \"نظام الحوادث الأمنية\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(true),\n                                    className: \"text-gray-400 hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_ChartBarIcon_Cog6ToothIcon_HomeIcon_ShieldCheckIcon_UserCircleIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.Bars3Icon, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\IR-Platform\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Layout, \"DHl2WrQkRhUiGs6dBiw/mybn0qg=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Layout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Layout);\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layout/Layout.tsx\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/ExclamationCircleIcon.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ExclamationCircleIcon.js ***!
  \*******************************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction ExclamationCircleIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\"\n    }));\n}\n_c = ExclamationCircleIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ExclamationCircleIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ExclamationCircleIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/ExclamationCircleIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/FireIcon.js":
/*!******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/FireIcon.js ***!
  \******************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction FireIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M15.362 5.214A8.252 8.252 0 0 1 12 21 8.25 8.25 0 0 1 6.038 7.047 8.287 8.287 0 0 0 9 9.601a8.983 8.983 0 0 1 3.361-6.867 8.21 8.21 0 0 0 3 2.48Z\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 18a3.75 3.75 0 0 0 .495-7.468 5.99 5.99 0 0 0-1.925 3.547 5.975 5.975 0 0 1-2.133-1.001A3.75 3.75 0 0 0 12 18Z\"\n    }));\n}\n_c = FireIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(FireIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"FireIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/FireIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js ***!
  \*************************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction ShieldCheckIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z\"\n    }));\n}\n_c = ShieldCheckIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ShieldCheckIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ShieldCheckIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\n"));

/***/ })

});